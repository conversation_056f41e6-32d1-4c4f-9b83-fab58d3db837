"""
系统测试脚本
"""
import os
import sys
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_environment():
    """测试环境配置"""
    print("🔧 测试环境配置...")
    
    # 检查API密钥
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ DEEPSEEK_API_KEY 未配置")
        return False
    else:
        print(f"✅ DEEPSEEK_API_KEY 已配置: {api_key[:10]}...")
    
    # 检查必要的包
    required_packages = [
        'langchain', 'modelscope', 'faiss',
        'streamlit', 'openai', 'sentence_transformers'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n📚 测试数据加载...")
    
    try:
        from data_loader import LegalDataLoader
        
        loader = LegalDataLoader()
        # 只测试加载少量数据
        print("正在下载测试数据...")
        df = loader.download_dataset()
        print(f"✅ 成功下载 {len(df)} 条记录")
        
        # 测试预处理
        processed_data = loader.preprocess_data(df.head(10))  # 只处理前10条
        print(f"✅ 成功预处理 {len(processed_data)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {str(e)}")
        return False

def test_vector_store():
    """测试向量数据库"""
    print("\n🔍 测试向量数据库...")
    
    try:
        from vector_store import VectorStoreManager
        from data_loader import LegalDataLoader
        
        # 加载少量测试数据
        loader = LegalDataLoader()
        processed_data = loader.load_processed_data()[:5]  # 只用前5条数据测试
        
        # 创建向量数据库
        vector_manager = VectorStoreManager()
        vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
        
        # 测试搜索
        results = vector_manager.search_similar("农业", k=2)
        print(f"✅ 向量搜索成功，找到 {len(results)} 个结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量数据库测试失败: {str(e)}")
        return False

def test_llm():
    """测试大语言模型"""
    print("\n🤖 测试DeepSeek API...")
    
    try:
        from legal_agent import DeepSeekLLM
        
        llm = DeepSeekLLM()
        response = llm._call("你好，请简单介绍一下自己。")
        
        if "抱歉" in response and "技术问题" in response:
            print(f"❌ API调用失败: {response}")
            return False
        else:
            print(f"✅ API调用成功: {response[:100]}...")
            return True
            
    except Exception as e:
        print(f"❌ LLM测试失败: {str(e)}")
        return False

def test_full_system():
    """测试完整系统"""
    print("\n🎯 测试完整系统...")
    
    try:
        from data_loader import LegalDataLoader
        from vector_store import VectorStoreManager
        from legal_agent import LegalAssistant
        
        # 使用少量数据测试
        loader = LegalDataLoader()
        processed_data = loader.load_processed_data()[:3]  # 只用前3条数据
        
        vector_manager = VectorStoreManager()
        vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
        
        assistant = LegalAssistant(vectorstore)
        
        # 测试问答
        test_question = "什么是农业法？"
        result = assistant.ask(test_question)
        
        print(f"✅ 系统测试成功")
        print(f"问题: {test_question}")
        print(f"回答: {result['answer'][:200]}...")
        print(f"参考文档数量: {len(result['source_documents'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整系统测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 法律知识小助手系统测试")
    print("=" * 50)
    
    tests = [
        ("环境配置", test_environment),
        ("数据加载", test_data_loading),
        ("向量数据库", test_vector_store),
        ("大语言模型", test_llm),
        ("完整系统", test_full_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"🏁 测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n启动命令:")
        print("  命令行界面: python run.py --mode cli")
        print("  Web界面:    python run.py --mode web")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")

if __name__ == "__main__":
    main()
