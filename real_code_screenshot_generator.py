#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实代码截图生成器
从项目实际代码文件中提取代码片段并生成截图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import numpy as np
import os
import re

class RealCodeScreenshotGenerator:
    def __init__(self):
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 代码高亮颜色配置 - VS Code Dark主题
        self.colors = {
            'background': '#1e1e1e',
            'text': '#d4d4d4',
            'keyword': '#569cd6',
            'string': '#ce9178',
            'comment': '#6a9955',
            'function': '#dcdcaa',
            'class': '#4ec9b0',
            'number': '#b5cea8',
            'border': '#404040'
        }
    
    def read_code_from_file(self, filename, start_line=1, end_line=None):
        """从文件中读取指定行数的代码"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if end_line is None:
                end_line = len(lines)
            
            # 提取指定行数
            code_lines = lines[start_line-1:end_line]
            
            # 添加行号
            numbered_lines = []
            for i, line in enumerate(code_lines, start_line):
                numbered_lines.append(f"{i:3d}  {line.rstrip()}")
            
            return '\n'.join(numbered_lines)
        except Exception as e:
            return f"# 读取文件失败: {e}"
    
    def create_code_screenshot(self, code_text, title, filename, width=14, height=10):
        """创建代码截图"""
        fig, ax = plt.subplots(figsize=(width, height))
        
        # 设置背景色
        fig.patch.set_facecolor(self.colors['background'])
        ax.set_facecolor(self.colors['background'])
        
        # 移除坐标轴
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 添加标题
        ax.text(0.5, 0.95, title, fontsize=18, fontweight='bold', 
                color='white', ha='center', va='top')
        
        # 添加代码文本框
        code_box = patches.FancyBboxPatch(
            (0.02, 0.05), 0.96, 0.85,
            boxstyle="round,pad=0.02",
            facecolor=self.colors['background'],
            edgecolor=self.colors['border'],
            linewidth=2
        )
        ax.add_patch(code_box)
        
        # 添加代码文本
        ax.text(0.05, 0.88, code_text, fontsize=11, fontfamily='monospace',
                color=self.colors['text'], ha='left', va='top',
                linespacing=1.2)
        
        # 保存截图
        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight', 
                   facecolor=self.colors['background'], edgecolor='none')
        plt.close()
        
        print(f"✅ 代码截图已保存: {filename}")
    
    def generate_all_real_screenshots(self):
        """生成所有真实代码截图"""
        
        # 1. 系统初始化代码 - 从comprehensive_legal_assistant.py
        print("📸 生成系统初始化代码截图...")
        init_code = self.read_code_from_file('comprehensive_legal_assistant.py', 22, 50)
        self.create_code_screenshot(
            init_code,
            "系统初始化 - ComprehensiveLegalAssistant类构造函数",
            "real_code_1_init.png"
        )
        
        # 2. 中文分词器代码
        print("📸 生成中文分词器代码截图...")
        tokenizer_code = self.read_code_from_file('comprehensive_legal_assistant.py', 26, 44)
        self.create_code_screenshot(
            tokenizer_code,
            "中文分词器实现 - 支持中英文混合文本处理",
            "real_code_2_tokenizer.png"
        )
        
        # 3. 文档搜索算法
        print("📸 生成文档搜索算法截图...")
        search_code = self.read_code_from_file('comprehensive_legal_assistant.py', 68, 86)
        self.create_code_screenshot(
            search_code,
            "文档搜索算法 - TF-IDF向量化与余弦相似度",
            "real_code_3_search.png"
        )
        
        # 4. AI问答核心逻辑
        print("📸 生成AI问答核心逻辑截图...")
        ask_code = self.read_code_from_file('comprehensive_legal_assistant.py', 88, 120)
        self.create_code_screenshot(
            ask_code,
            "AI问答核心逻辑 - 多步骤智能问答流程",
            "real_code_4_ask.png"
        )
        
        # 5. Web界面初始化
        print("📸 生成Web界面初始化截图...")
        web_init_code = self.read_code_from_file('web_interface.py', 23, 43)
        self.create_code_screenshot(
            web_init_code,
            "Web界面初始化 - Streamlit应用配置",
            "real_code_5_web_init.png"
        )
        
        # 6. 向量存储管理
        print("📸 生成向量存储管理截图...")
        if os.path.exists('vector_store.py'):
            vector_code = self.read_code_from_file('vector_store.py', 1, 30)
            self.create_code_screenshot(
                vector_code,
                "向量存储管理 - FAISS向量数据库操作",
                "real_code_6_vector.png"
            )
        
        # 7. 法律知识库结构
        print("📸 生成法律知识库结构截图...")
        if os.path.exists('expanded_legal_kb.py'):
            kb_code = self.read_code_from_file('expanded_legal_kb.py', 1, 25)
            self.create_code_screenshot(
                kb_code,
                "法律知识库结构 - 多领域法律文档数据",
                "real_code_7_kb.png"
            )
        
        # 8. CLI界面实现
        print("📸 生成CLI界面实现截图...")
        if os.path.exists('cli_interface.py'):
            cli_code = self.read_code_from_file('cli_interface.py', 1, 30)
            self.create_code_screenshot(
                cli_code,
                "CLI界面实现 - 命令行交互界面",
                "real_code_8_cli.png"
            )
        
        print("\n🎉 所有真实代码截图生成完成！")
        
        # 列出生成的截图文件
        screenshots = []
        for i in range(1, 9):
            filename = f"real_code_{i}_*.png"
            import glob
            files = glob.glob(filename)
            screenshots.extend(files)
        
        print("生成的截图文件:")
        for screenshot in sorted(screenshots):
            if os.path.exists(screenshot):
                print(f"  • {screenshot}")

def main():
    """主函数"""
    generator = RealCodeScreenshotGenerator()
    generator.generate_all_real_screenshots()

if __name__ == "__main__":
    main()
