#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实代码截图生成器
从项目实际代码文件中提取代码片段并生成截图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import numpy as np
import os
import re

class RealCodeScreenshotGenerator:
    def __init__(self):
        # 设置支持中文的字体
        self.setup_chinese_fonts()

    def setup_chinese_fonts(self):
        """设置支持中文显示的字体"""
        try:
            # 获取系统可用字体
            available_fonts = [f.name for f in font_manager.fontManager.ttflist]

            # 中文字体优先级列表
            chinese_fonts = [
                'Microsoft YaHei',     # 微软雅黑
                'SimHei',             # 黑体
                'SimSun',             # 宋体
                'KaiTi',              # 楷体
                'FangSong',           # 仿宋
                'STHeiti',            # 华文黑体
                'PingFang SC',        # 苹果字体
                'Noto Sans CJK SC',   # Google字体
                'Source Han Sans SC', # Adobe字体
                'WenQuanYi Micro Hei' # Linux字体
            ]

            # 找到第一个可用的中文字体
            selected_font = None
            for font in chinese_fonts:
                if font in available_fonts:
                    selected_font = font
                    break

            if selected_font:
                plt.rcParams['font.sans-serif'] = [selected_font, 'DejaVu Sans', 'Arial']
                print(f"✅ 使用中文字体: {selected_font}")
            else:
                # 如果没有找到中文字体，使用系统默认字体并警告
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                print("⚠️ 未找到中文字体，可能显示为方框")

            plt.rcParams['axes.unicode_minus'] = False

        except Exception as e:
            print(f"❌ 字体设置失败: {e}")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False
        
        # 代码高亮颜色配置 - VS Code Dark主题
        self.colors = {
            'background': '#1e1e1e',
            'text': '#d4d4d4',
            'keyword': '#569cd6',
            'string': '#ce9178',
            'comment': '#6a9955',
            'function': '#dcdcaa',
            'class': '#4ec9b0',
            'number': '#b5cea8',
            'border': '#404040'
        }
    
    def read_code_from_file(self, filename, start_line=1, end_line=None):
        """从文件中读取指定行数的代码"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if end_line is None:
                end_line = len(lines)
            
            # 提取指定行数
            code_lines = lines[start_line-1:end_line]
            
            # 添加行号
            numbered_lines = []
            for i, line in enumerate(code_lines, start_line):
                numbered_lines.append(f"{i:3d}  {line.rstrip()}")
            
            return '\n'.join(numbered_lines)
        except Exception as e:
            return f"# 读取文件失败: {e}"

    def process_code_for_display(self, code_text):
        """处理代码文本，将中文注释转换为英文以避免显示问题"""
        # 中文注释映射表
        chinese_to_english = {
            '# 初始化TF-IDF向量化器': '# Initialize TF-IDF vectorizer',
            '# 配置中文分词器': '# Configure Chinese tokenizer',
            '# 加载法律文档数据': '# Load legal document data',
            '# 建立向量索引': '# Build vector index',
            '# 配置DeepSeek API': '# Configure DeepSeek API',
            '# 中文分词实现': '# Chinese tokenization implementation',
            '# 正则表达式匹配中文字符': '# Regex matching Chinese characters',
            '# TF-IDF向量化': '# TF-IDF vectorization',
            '# 余弦相似度计算': '# Cosine similarity calculation',
            '# 文档检索与筛选': '# Document retrieval and filtering',
            '# 法律领域识别': '# Legal domain identification',
            '# 上下文构建': '# Context construction',
            '# AI模型调用': '# AI model invocation',
            '# 结构化响应': '# Structured response',
            '# 缓存机制优化': '# Cache mechanism optimization',
            '# 组件化设计': '# Component-based design',
            '# 响应式布局': '# Responsive layout',
            '# 用户友好交互': '# User-friendly interaction',
            '# 向量化数据管理': '# Vectorized data management',
            '# 高效索引建立': '# Efficient index construction',
            '# 结构化知识组织': '# Structured knowledge organization',
            '# 多层级分类体系': '# Multi-level classification system',
            '# 简洁的交互设计': '# Clean interaction design',
            '# 丰富的命令支持': '# Rich command support',
            '"""': '"""',
            '智能法律知识助手系统': 'Intelligent Legal Knowledge Assistant System',
            '基于LangChain和DeepSeek的多领域法律咨询AI': 'Multi-domain Legal Consultation AI based on LangChain and DeepSeek',
            '法律文档向量化存储和检索系统': 'Legal Document Vectorization Storage and Retrieval System',
            '支持中文分词的TF-IDF实现': 'TF-IDF Implementation with Chinese Word Segmentation Support'
        }

        processed_text = code_text
        for chinese, english in chinese_to_english.items():
            processed_text = processed_text.replace(chinese, english)

        return processed_text
    
    def create_code_screenshot(self, code_text, title, filename, width=14, height=10):
        """创建代码截图"""
        fig, ax = plt.subplots(figsize=(width, height))
        
        # 设置背景色
        fig.patch.set_facecolor(self.colors['background'])
        ax.set_facecolor(self.colors['background'])
        
        # 移除坐标轴
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 添加标题
        ax.text(0.5, 0.95, title, fontsize=18, fontweight='bold', 
                color='white', ha='center', va='top')
        
        # 添加代码文本框
        code_box = patches.FancyBboxPatch(
            (0.02, 0.05), 0.96, 0.85,
            boxstyle="round,pad=0.02",
            facecolor=self.colors['background'],
            edgecolor=self.colors['border'],
            linewidth=2
        )
        ax.add_patch(code_box)
        
        # 处理代码文本，将中文注释替换为英文
        processed_code = self.process_code_for_display(code_text)

        # 添加代码文本 - 使用等宽字体
        ax.text(0.05, 0.88, processed_code, fontsize=10,
                fontfamily='monospace', color=self.colors['text'],
                ha='left', va='top', linespacing=1.3)
        
        # 保存截图
        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight', 
                   facecolor=self.colors['background'], edgecolor='none')
        plt.close()
        
        print(f"✅ 代码截图已保存: {filename}")
    
    def generate_all_real_screenshots(self):
        """生成所有真实代码截图"""
        
        # 1. 系统初始化代码 - 从comprehensive_legal_assistant.py
        print("📸 生成系统初始化代码截图...")
        init_code = self.read_code_from_file('comprehensive_legal_assistant.py', 22, 50)
        self.create_code_screenshot(
            init_code,
            "系统初始化 - ComprehensiveLegalAssistant类构造函数",
            "real_code_1_init.png"
        )
        
        # 2. 中文分词器代码
        print("📸 生成中文分词器代码截图...")
        tokenizer_code = self.read_code_from_file('comprehensive_legal_assistant.py', 26, 44)
        self.create_code_screenshot(
            tokenizer_code,
            "中文分词器实现 - 支持中英文混合文本处理",
            "real_code_2_tokenizer.png"
        )
        
        # 3. 文档搜索算法
        print("📸 生成文档搜索算法截图...")
        search_code = self.read_code_from_file('comprehensive_legal_assistant.py', 68, 86)
        self.create_code_screenshot(
            search_code,
            "文档搜索算法 - TF-IDF向量化与余弦相似度",
            "real_code_3_search.png"
        )
        
        # 4. AI问答核心逻辑
        print("📸 生成AI问答核心逻辑截图...")
        ask_code = self.read_code_from_file('comprehensive_legal_assistant.py', 88, 120)
        self.create_code_screenshot(
            ask_code,
            "AI问答核心逻辑 - 多步骤智能问答流程",
            "real_code_4_ask.png"
        )
        
        # 5. Web界面初始化
        print("📸 生成Web界面初始化截图...")
        web_init_code = self.read_code_from_file('web_interface.py', 23, 43)
        self.create_code_screenshot(
            web_init_code,
            "Web界面初始化 - Streamlit应用配置",
            "real_code_5_web_init.png"
        )
        
        # 6. 向量存储管理
        print("📸 生成向量存储管理截图...")
        if os.path.exists('vector_store.py'):
            vector_code = self.read_code_from_file('vector_store.py', 1, 30)
            self.create_code_screenshot(
                vector_code,
                "向量存储管理 - FAISS向量数据库操作",
                "real_code_6_vector.png"
            )
        
        # 7. 法律知识库结构
        print("📸 生成法律知识库结构截图...")
        if os.path.exists('expanded_legal_kb.py'):
            kb_code = self.read_code_from_file('expanded_legal_kb.py', 1, 25)
            self.create_code_screenshot(
                kb_code,
                "法律知识库结构 - 多领域法律文档数据",
                "real_code_7_kb.png"
            )
        
        # 8. CLI界面实现
        print("📸 生成CLI界面实现截图...")
        if os.path.exists('cli_interface.py'):
            cli_code = self.read_code_from_file('cli_interface.py', 1, 30)
            self.create_code_screenshot(
                cli_code,
                "CLI界面实现 - 命令行交互界面",
                "real_code_8_cli.png"
            )
        
        print("\n🎉 所有真实代码截图生成完成！")
        
        # 列出生成的截图文件
        screenshots = []
        for i in range(1, 9):
            filename = f"real_code_{i}_*.png"
            import glob
            files = glob.glob(filename)
            screenshots.extend(files)
        
        print("生成的截图文件:")
        for screenshot in sorted(screenshots):
            if os.path.exists(screenshot):
                print(f"  • {screenshot}")

def main():
    """主函数"""
    generator = RealCodeScreenshotGenerator()
    generator.generate_all_real_screenshots()

if __name__ == "__main__":
    main()
