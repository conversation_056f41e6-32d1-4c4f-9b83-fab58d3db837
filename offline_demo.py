"""
离线演示版本 - 不依赖外部模型下载
"""
import os
import json
import logging
from typing import List, Dict, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleVectorStore:
    """简单的向量存储，使用TF-IDF"""
    
    def __init__(self):
        # 简单的中文分词函数
        def chinese_tokenizer(text):
            import re
            # 简单的中文字符分词
            tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
            # 生成单字符和双字符组合
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    # 添加字符级别的n-gram
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result

        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        self.documents = []
        self.vectors = None
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """添加文档"""
        self.documents = documents
        texts = [doc['content'] for doc in documents]
        self.vectors = self.vectorizer.fit_transform(texts)
        logger.info(f"已添加 {len(documents)} 个文档到向量存储")
    
    def search(self, query: str, k: int = 3) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        if self.vectors is None:
            return []
        
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        
        # 获取最相似的k个文档
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:  # 只返回有相似度的结果
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(similarities[idx])
                })
        
        return results

class SimpleLegalAssistant:
    """简单的法律助手"""
    
    def __init__(self, vector_store: SimpleVectorStore):
        self.vector_store = vector_store
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            self.client = None
            logger.warning("未配置DeepSeek API密钥，将使用模板回答")
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答问题"""
        # 搜索相关文档
        relevant_docs = self.vector_store.search(question, k=3)
        
        if not relevant_docs:
            return {
                "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。",
                "source_documents": []
            }
        
        # 构建上下文
        context = "\n\n".join([doc['content'] for doc in relevant_docs])
        
        if self.client:
            # 使用DeepSeek API生成回答
            try:
                prompt = f"""你是一个专业的法律知识助手，专门回答农业相关的法律问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

用户问题: {question}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 如果知识库中没有相关信息，请诚实地说明
3. 回答要准确、专业、易懂
4. 可以适当引用具体的法律条文或案例
5. 如果问题涉及具体的法律建议，请提醒用户咨询专业律师

回答:"""

                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1000,
                    temperature=0.7
                )
                
                answer = response.choices[0].message.content
                
            except Exception as e:
                logger.error(f"API调用失败: {str(e)}")
                answer = f"基于相关法律知识，{relevant_docs[0]['content'][:200]}... (API调用失败，显示部分相关内容)"
        else:
            # 使用模板回答
            answer = f"根据相关法律规定，{relevant_docs[0]['content']}"
        
        return {
            "answer": answer,
            "source_documents": relevant_docs
        }

def main():
    """主演示函数"""
    print("🏛️  法律知识小助手离线演示")
    print("=" * 50)
    
    # 创建测试数据
    test_documents = [
        {
            'content': '农业补贴是国家为了支持农业发展，提高农民收入而给予的资金支持。主要包括种粮直补、农资综合补贴、良种补贴、农机购置补贴等。这些补贴政策旨在调动农民种粮积极性，保障国家粮食安全。',
            'metadata': {'source': 'test', 'title': '农业补贴政策', 'type': '政策解读'}
        },
        {
            'content': '土地承包经营权是农民依法对其承包土地享有的占有、使用和收益的权利。根据《农村土地承包法》，农民的土地承包经营权受法律保护，任何组织和个人不得侵犯。承包期内，发包方不得收回承包地。',
            'metadata': {'source': 'test', 'title': '土地承包权保护', 'type': '法律条文'}
        },
        {
            'content': '农产品质量安全法规定，农产品生产者应当按照法律、法规和农产品质量安全标准从事生产活动。禁止在农产品生产过程中使用国家禁用的农药、兽药等化学物质。农产品生产记录应当保存二年。',
            'metadata': {'source': 'test', 'title': '农产品质量安全', 'type': '法律条文'}
        },
        {
            'content': '农民专业合作社是在农村家庭承包经营基础上，同类农产品的生产经营者或者同类农业生产经营服务的提供者、利用者，自愿联合、民主管理的互助性经济组织。合作社享受国家规定的优惠政策。',
            'metadata': {'source': 'test', 'title': '农民专业合作社', 'type': '法律定义'}
        },
        {
            'content': '农业保险是指保险机构根据农业保险合同，对被保险人在农业生产过程中因保险标的遭受约定的自然灾害、意外事故、疫病、疾病等保险事故所造成的财产损失，承担赔偿保险金责任的保险活动。',
            'metadata': {'source': 'test', 'title': '农业保险', 'type': '法律定义'}
        }
    ]
    
    # 创建向量存储
    vector_store = SimpleVectorStore()
    vector_store.add_documents(test_documents)
    
    # 创建法律助手
    assistant = SimpleLegalAssistant(vector_store)
    
    print("✅ 系统初始化完成！")
    print("\n📚 已加载以下法律知识:")
    for i, doc in enumerate(test_documents, 1):
        print(f"  {i}. {doc['metadata']['title']} ({doc['metadata']['type']})")
    
    # 测试问题
    test_questions = [
        "农业补贴有哪些类型？",
        "农民的土地承包权受到什么保护？",
        "农产品质量安全有什么要求？",
        "什么是农民专业合作社？",
        "农业保险是什么？"
    ]
    
    print(f"\n🤖 开始问答演示:")
    print("-" * 50)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n问题 {i}: {question}")
        result = assistant.ask(question)
        print(f"回答: {result['answer']}")
        print(f"参考文档: {len(result['source_documents'])} 个")
        if result['source_documents']:
            print(f"主要来源: {result['source_documents'][0]['metadata']['title']}")
    
    print(f"\n🎉 演示完成！")
    print("\n💡 使用说明:")
    print("  • 这是一个离线演示版本，使用TF-IDF进行文档检索")
    print("  • 如果配置了DeepSeek API密钥，将使用AI生成回答")
    print("  • 否则将使用模板回答")
    print("  • 实际使用时可以加载更多的法律文档数据")

if __name__ == "__main__":
    main()
