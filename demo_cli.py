"""
法律知识小助手演示版命令行界面
"""
import os
import json
import logging
from typing import List, Dict, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出
logger = logging.getLogger(__name__)

class SimpleVectorStore:
    """简单的向量存储，使用TF-IDF"""
    
    def __init__(self):
        # 简单的中文分词函数
        def chinese_tokenizer(text):
            import re
            tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result
        
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        self.documents = []
        self.vectors = None
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        """添加文档"""
        self.documents = documents
        texts = [doc['content'] for doc in documents]
        self.vectors = self.vectorizer.fit_transform(texts)
    
    def search(self, query: str, k: int = 3) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        if self.vectors is None:
            return []
        
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(similarities[idx])
                })
        
        return results

class SimpleLegalAssistant:
    """简单的法律助手"""
    
    def __init__(self, vector_store: SimpleVectorStore):
        self.vector_store = vector_store
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            self.client = None
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答问题"""
        relevant_docs = self.vector_store.search(question, k=3)
        
        if not relevant_docs:
            return {
                "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。",
                "source_documents": []
            }
        
        context = "\n\n".join([doc['content'] for doc in relevant_docs])
        
        if self.client:
            try:
                prompt = f"""你是一个专业的法律知识助手，专门回答农业相关的法律问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

用户问题: {question}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 回答要准确、专业、易懂
3. 如果问题涉及具体的法律建议，请提醒用户咨询专业律师

回答:"""

                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1000,
                    temperature=0.7
                )
                
                answer = response.choices[0].message.content
                
            except Exception as e:
                answer = f"基于相关法律知识：{relevant_docs[0]['content'][:200]}... (API调用失败)"
        else:
            answer = f"根据相关法律规定：{relevant_docs[0]['content']}"
        
        return {
            "answer": answer,
            "source_documents": relevant_docs
        }

def initialize_system():
    """初始化系统"""
    # 法律知识库数据
    legal_documents = [
        {
            'content': '农业补贴是国家为了支持农业发展，提高农民收入而给予的资金支持。主要包括种粮直补、农资综合补贴、良种补贴、农机购置补贴等。这些补贴政策旨在调动农民种粮积极性，保障国家粮食安全。申请补贴需要提供相关证明材料，如土地承包证、种植证明等。',
            'metadata': {'source': 'policy', 'title': '农业补贴政策', 'type': '政策解读'}
        },
        {
            'content': '土地承包经营权是农民依法对其承包土地享有的占有、使用和收益的权利。根据《农村土地承包法》，农民的土地承包经营权受法律保护，任何组织和个人不得侵犯。承包期内，发包方不得收回承包地。承包期为30年，到期后可以延长。',
            'metadata': {'source': 'law', 'title': '土地承包权保护', 'type': '法律条文'}
        },
        {
            'content': '农产品质量安全法规定，农产品生产者应当按照法律、法规和农产品质量安全标准从事生产活动。禁止在农产品生产过程中使用国家禁用的农药、兽药等化学物质。农产品生产记录应当保存二年。违反规定的，将面临罚款等法律后果。',
            'metadata': {'source': 'law', 'title': '农产品质量安全', 'type': '法律条文'}
        },
        {
            'content': '农民专业合作社是在农村家庭承包经营基础上，同类农产品的生产经营者或者同类农业生产经营服务的提供者、利用者，自愿联合、民主管理的互助性经济组织。合作社享受国家规定的优惠政策，包括税收减免、财政补贴等。',
            'metadata': {'source': 'law', 'title': '农民专业合作社', 'type': '法律定义'}
        },
        {
            'content': '农业保险是指保险机构根据农业保险合同，对被保险人在农业生产过程中因保险标的遭受约定的自然灾害、意外事故、疫病、疾病等保险事故所造成的财产损失，承担赔偿保险金责任的保险活动。国家鼓励和支持发展农业保险。',
            'metadata': {'source': 'law', 'title': '农业保险', 'type': '法律定义'}
        },
        {
            'content': '农村土地流转是指农村家庭承包的土地通过合法的形式，保留承包权，将经营权转让给其他农户或经济组织的行为。土地流转必须遵循依法、自愿、有偿的原则。流转期限不得超过承包期的剩余期限。',
            'metadata': {'source': 'law', 'title': '农村土地流转', 'type': '法律条文'}
        }
    ]
    
    # 创建向量存储和助手
    vector_store = SimpleVectorStore()
    vector_store.add_documents(legal_documents)
    assistant = SimpleLegalAssistant(vector_store)
    
    return assistant, legal_documents

def main():
    """主函数"""
    print("⚖️  法律知识小助手")
    print("=" * 50)
    print("🏛️  专业的综合法律咨询助手")
    print("📚  基于法律知识库和DeepSeek AI")
    print("🔧  涵盖农业法、劳动法、婚姻法、房产法等多个领域")
    print("=" * 50)
    
    # 初始化系统
    print("🔧 正在初始化系统...")
    assistant, documents = initialize_system()
    
    print("✅ 系统初始化完成！")
    print(f"📖 已加载 {len(documents)} 条法律知识")
    
    # 显示帮助信息
    print("\n💡 使用说明:")
    print("  • 输入您的法律问题，我会为您详细解答")
    print("  • 输入 'help' 查看示例问题")
    print("  • 输入 'quit' 或 'exit' 退出程序")
    print("=" * 50)
    
    # 主循环
    while True:
        try:
            user_input = input("\n💬 请输入您的法律问题: ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("\n👋 感谢使用法律知识小助手，再见！")
                break
            
            if user_input.lower() in ['help', '帮助']:
                print("\n📋 示例问题:")
                print("🌾 农业法:")
                print("  • 农业补贴有哪些类型？")
                print("  • 农民的土地承包权如何保护？")
                print("💼 劳动法:")
                print("  • 劳动合同试用期最长多久？")
                print("  • 加班费如何计算？")
                print("💒 婚姻家庭法:")
                print("  • 法定结婚年龄是多少？")
                print("  • 离婚后子女抚养权如何确定？")
                print("🏠 房产法:")
                print("  • 房屋买卖需要注意什么？")
                print("  • 租房合同最长期限是多久？")
                print("🛒 消费者权益:")
                print("  • 网购可以无理由退货吗？")
                print("  • 遇到消费欺诈如何维权？")
                print("🚗 交通法:")
                print("  • 酒驾会受到什么处罚？")
                print("  • 交通事故责任如何认定？")
                continue
            
            print("\n🔍 正在查询相关法律信息...")
            result = assistant.ask(user_input)
            
            print("\n🤖 法律助手回答:")
            print("-" * 40)
            print(result['answer'])
            
            if result['source_documents']:
                print(f"\n📚 参考文档 ({len(result['source_documents'])} 个):")
                for i, doc in enumerate(result['source_documents'][:2], 1):
                    print(f"  {i}. {doc['metadata']['title']} ({doc['metadata']['type']})")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 处理您的问题时出现错误: {str(e)}")

if __name__ == "__main__":
    main()
