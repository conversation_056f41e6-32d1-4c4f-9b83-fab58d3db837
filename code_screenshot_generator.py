#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码截图生成器
为PPT演示生成美观的代码截图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import numpy as np
import os

class CodeScreenshotGenerator:
    def __init__(self):
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 代码高亮颜色配置
        self.colors = {
            'background': '#1e1e1e',
            'text': '#d4d4d4',
            'keyword': '#569cd6',
            'string': '#ce9178',
            'comment': '#6a9955',
            'function': '#dcdcaa',
            'class': '#4ec9b0',
            'number': '#b5cea8'
        }
    
    def create_code_screenshot(self, code_text, title, filename, width=12, height=8):
        """创建代码截图"""
        fig, ax = plt.subplots(figsize=(width, height))
        
        # 设置背景色
        fig.patch.set_facecolor(self.colors['background'])
        ax.set_facecolor(self.colors['background'])
        
        # 移除坐标轴
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 添加标题
        ax.text(0.5, 0.95, title, fontsize=16, fontweight='bold', 
                color='white', ha='center', va='top')
        
        # 添加代码文本
        ax.text(0.05, 0.88, code_text, fontsize=10, fontfamily='monospace',
                color=self.colors['text'], ha='left', va='top',
                bbox=dict(boxstyle="round,pad=0.02", facecolor=self.colors['background'], 
                         edgecolor='#404040', linewidth=1))
        
        # 保存截图
        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight', 
                   facecolor=self.colors['background'], edgecolor='none')
        plt.close()
        
        print(f"✅ 代码截图已保存: {filename}")
    
    def generate_all_screenshots(self):
        """生成所有关键代码的截图"""
        
        # 1. 系统初始化代码
        init_code = '''class ComprehensiveLegalAssistant:
    """综合法律助手"""
    
    def __init__(self):
        # 初始化中文分词器
        def chinese_tokenizer(text):
            import re
            tokens = re.findall(r'[\\u4e00-\\u9fff]+|[a-zA-Z0-9]+', text)
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result
        
        # 初始化TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        
        # 加载法律文档并向量化
        self.documents = EXPANDED_LEGAL_DOCUMENTS
        texts = [doc['content'] for doc in self.documents]
        self.vectors = self.vectorizer.fit_transform(texts)'''
        
        self.create_code_screenshot(
            init_code, 
            "核心类初始化 - 中文分词与向量化", 
            "code_screenshot_1_init.png"
        )
        
        # 2. 文档搜索算法
        search_code = '''def search_documents(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
    """搜索相关法律文档 - TF-IDF + 余弦相似度"""
    if self.vectors is None:
        return []
    
    # 将查询转换为TF-IDF向量
    query_vector = self.vectorizer.transform([query])
    
    # 计算查询与所有文档的余弦相似度
    similarities = cosine_similarity(query_vector, self.vectors).flatten()
    
    # 获取相似度最高的k个文档索引
    top_indices = np.argsort(similarities)[::-1][:k]
    
    # 构建结果列表
    results = []
    for idx in top_indices:
        if similarities[idx] > 0:  # 过滤相似度为0的文档
            results.append({
                'content': self.documents[idx]['content'],
                'metadata': self.documents[idx]['metadata'],
                'score': float(similarities[idx])
            })
    
    return results'''
        
        self.create_code_screenshot(
            search_code,
            "智能文档搜索 - TF-IDF向量检索算法",
            "code_screenshot_2_search.png"
        )
        
        # 3. AI对话引擎
        ai_code = '''def ask(self, question: str) -> Dict[str, Any]:
    """AI法律问答核心引擎"""
    # 第一步: 搜索相关法律文档
    relevant_docs = self.search_documents(question, k=5)
    
    if not relevant_docs:
        return {
            "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。",
            "source_documents": [],
            "legal_areas": []
        }
    
    # 第二步: 分析涉及的法律领域
    legal_areas = list(set([doc['metadata']['type'] for doc in relevant_docs]))
    
    # 第三步: 构建结构化上下文
    context = "\\n\\n".join([
        f"【{doc['metadata']['type']} - {doc['metadata']['title']}】\\n{doc['content']}" 
        for doc in relevant_docs
    ])
    
    # 第四步: 调用DeepSeek API生成专业回答
    if self.client:
        try:
            prompt = f"""你是专业的法律知识助手，基于以下法律知识回答问题:
            
相关法律知识:
{context}

用户问题: {question}
涉及法律领域: {', '.join(legal_areas)}

请提供准确、专业、易懂的法律建议。"""'''
        
        self.create_code_screenshot(
            ai_code,
            "AI对话引擎 - 多步骤智能问答流程",
            "code_screenshot_3_ai.png"
        )
        
        # 4. 知识库结构
        kb_code = '''# 扩展法律知识库 - 结构化数据设计
EXPANDED_LEGAL_DOCUMENTS = [
    {
        "content": "劳动合同是劳动者与用人单位确立劳动关系，明确双方权利和义务的协议...",
        "metadata": {
            "type": "劳动法",
            "title": "劳动合同基本规定",
            "keywords": ["劳动合同", "用人单位", "劳动者权益", "合同解除"],
            "difficulty": "基础",
            "applicability": "全国通用"
        }
    },
    {
        "content": "婚姻法规定，结婚必须男女双方完全自愿，不许任何一方对他方加以强迫...",
        "metadata": {
            "type": "婚姻法", 
            "title": "结婚条件与程序",
            "keywords": ["结婚", "婚姻自由", "法定条件", "结婚登记"],
            "difficulty": "基础",
            "applicability": "全国通用"
        }
    }
    # ... 更多法律文档 (总计500+条)
]

# 支持的9大法律领域
LEGAL_AREAS = [
    "劳动法", "婚姻法", "合同法", "刑法", "民法", 
    "行政法", "消费者权益", "交通法", "知识产权法"
]'''
        
        self.create_code_screenshot(
            kb_code,
            "知识库架构 - 结构化法律文档存储",
            "code_screenshot_4_kb.png"
        )
        
        # 5. Web界面实现
        web_code = '''# Streamlit Web界面 - 用户友好的图形界面
import streamlit as st

def main():
    st.set_page_config(
        page_title="智能法律知识助手",
        page_icon="⚖️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 主标题和介绍
    st.title("⚖️ 智能法律知识助手")
    st.markdown("🎯 **专业的多领域法律咨询AI系统**")
    st.markdown("支持劳动法、婚姻法、合同法等9大法律领域")
    
    # 初始化法律助手
    if 'assistant' not in st.session_state:
        with st.spinner("正在初始化法律知识库..."):
            st.session_state.assistant = ComprehensiveLegalAssistant()
        st.success("✅ 系统初始化完成！")
    
    # 用户输入区域
    st.subheader("💬 请输入您的法律问题")
    question = st.text_area(
        "详细描述您遇到的法律问题:",
        height=120,
        placeholder="例如：我想了解劳动合同解除的相关规定..."
    )
    
    # 查询按钮和结果展示
    if st.button("🔍 获取专业法律建议", type="primary"):
        if question.strip():
            with st.spinner("🤖 AI正在分析您的问题..."):
                result = st.session_state.assistant.ask(question)
            
            # 显示结果
            st.success("✅ 分析完成！")
            
            # 涉及的法律领域
            if result['legal_areas']:
                st.info(f"📋 涉及法律领域: {', '.join(result['legal_areas'])}")
            
            # AI回答
            st.subheader("💡 专业法律建议")
            st.write(result['answer'])
            
            # 参考文档
            if result['source_documents']:
                with st.expander(f"📚 参考法律文档 ({len(result['source_documents'])}条)"):
                    for i, doc in enumerate(result['source_documents'], 1):
                        st.write(f"**{i}. {doc['metadata']['title']}** (相似度: {doc['score']:.3f})")
                        st.write(doc['content'][:200] + "...")
                        st.write("---")
        else:
            st.warning("⚠️ 请输入您的法律问题")

if __name__ == "__main__":
    main()'''
        
        self.create_code_screenshot(
            web_code,
            "Web界面实现 - Streamlit用户交互设计",
            "code_screenshot_5_web.png",
            width=14, height=10
        )
        
        print("\n🎉 所有代码截图生成完成！")
        print("生成的截图文件:")
        screenshots = [
            "code_screenshot_1_init.png",
            "code_screenshot_2_search.png", 
            "code_screenshot_3_ai.png",
            "code_screenshot_4_kb.png",
            "code_screenshot_5_web.png"
        ]
        for screenshot in screenshots:
            print(f"  • {screenshot}")

def main():
    """主函数"""
    generator = CodeScreenshotGenerator()
    generator.generate_all_screenshots()

if __name__ == "__main__":
    main()
