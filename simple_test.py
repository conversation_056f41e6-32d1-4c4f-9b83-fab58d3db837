"""
简化的系统测试脚本
"""
import os
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试向量数据库
        from vector_store import VectorStoreManager
        from langchain.docstore.document import Document
        
        # 创建测试文档
        test_documents = [
            {
                'content': '农业补贴是国家为了支持农业发展，提高农民收入而给予的资金支持。主要包括种粮直补、农资综合补贴、良种补贴等。',
                'metadata': {'source': 'test', 'title': '农业补贴政策'}
            },
            {
                'content': '土地承包经营权是农民依法对其承包土地享有的占有、使用和收益的权利。国家保护农民的土地承包经营权。',
                'metadata': {'source': 'test', 'title': '土地承包权'}
            },
            {
                'content': '农产品质量安全法规定，农产品生产者应当按照法律、法规和农产品质量安全标准从事生产活动。',
                'metadata': {'source': 'test', 'title': '农产品质量安全'}
            }
        ]
        
        print("✅ 创建测试数据成功")
        
        # 测试向量数据库
        vector_manager = VectorStoreManager()
        vectorstore = vector_manager.get_or_create_vectorstore(test_documents)
        
        print("✅ 向量数据库创建成功")
        
        # 测试搜索
        results = vector_manager.search_similar("农业补贴", k=2)
        print(f"✅ 向量搜索成功，找到 {len(results)} 个结果")
        
        # 测试法律助手
        from legal_agent import LegalAssistant
        assistant = LegalAssistant(vectorstore)
        
        print("✅ 法律助手创建成功")
        
        # 测试问答
        test_question = "什么是农业补贴？"
        result = assistant.ask(test_question)
        
        print(f"✅ 问答测试成功")
        print(f"问题: {test_question}")
        print(f"回答: {result['answer'][:200]}...")
        print(f"参考文档数量: {len(result['source_documents'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 法律知识小助手简化测试")
    print("=" * 50)
    
    if test_basic_functionality():
        print("\n🎉 基本功能测试通过！")
        print("\n📋 使用说明:")
        print("1. 命令行界面: python run.py --mode cli")
        print("2. Web界面: python run.py --mode web")
        print("\n⚠️  注意: 由于数据下载可能存在兼容性问题，建议直接使用预设的测试数据。")
    else:
        print("\n❌ 基本功能测试失败，请检查配置。")

if __name__ == "__main__":
    main()
