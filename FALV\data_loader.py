import os
import json
import pandas as pd
from typing import List, Dict, Any
from modelscope.msdatasets import MsDataset
from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from config import Config
import jieba

class DataLoader:
    """数据加载和预处理类"""
    
    def __init__(self):
        self.config = Config()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.CHUNK_SIZE,
            chunk_overlap=self.config.CHUNK_OVERLAP,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )
    
    def load_dataset_from_modelscope(self) -> List[Dict[str, Any]]:
        """从ModelScope加载数据集"""
        try:
            print("正在从ModelScope下载数据集...")
            ds = MsDataset.load(
                self.config.DATASET_NAME, 
                subset_name=self.config.DATASET_SUBSET, 
                split=self.config.DATASET_SPLIT
            )
            
            # 转换为列表格式
            data_list = []
            for item in ds:
                data_list.append(item)
            
            print(f"成功加载 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            print(f"加载数据集失败: {e}")
            return []
    
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ""
        
        # 基本清理
        text = text.strip()
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 去除多余空白
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        text = '\n'.join(lines)
        
        return text
    
    def create_documents(self, data_list: List[Dict[str, Any]]) -> List[Document]:
        """将数据转换为LangChain Document对象"""
        documents = []
        
        for i, item in enumerate(data_list):
            try:
                # 根据数据结构提取文本内容
                # 这里需要根据实际的数据结构进行调整
                content = ""
                metadata = {"source": f"document_{i}"}
                
                # 尝试不同的字段名来提取内容
                possible_content_fields = ['text', 'content', 'question', 'answer', 'law_content', 'description']
                possible_metadata_fields = ['title', 'category', 'law_name', 'type']
                
                for field in possible_content_fields:
                    if field in item and item[field]:
                        if content:
                            content += f"\n{item[field]}"
                        else:
                            content = str(item[field])
                
                # 提取元数据
                for field in possible_metadata_fields:
                    if field in item and item[field]:
                        metadata[field] = str(item[field])
                
                # 如果没有找到内容字段，将整个item转为字符串
                if not content:
                    content = str(item)
                
                # 预处理文本
                content = self.preprocess_text(content)
                
                if content:
                    documents.append(Document(
                        page_content=content,
                        metadata=metadata
                    ))
                    
            except Exception as e:
                print(f"处理第 {i} 条数据时出错: {e}")
                continue
        
        print(f"成功创建 {len(documents)} 个文档对象")
        return documents
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为小块"""
        try:
            split_docs = self.text_splitter.split_documents(documents)
            print(f"文档分割完成，共 {len(split_docs)} 个文档块")
            return split_docs
        except Exception as e:
            print(f"文档分割失败: {e}")
            return documents
    
    def load_and_process_data(self) -> List[Document]:
        """完整的数据加载和处理流程"""
        print("开始数据加载和处理...")
        
        # 1. 从ModelScope加载数据
        data_list = self.load_dataset_from_modelscope()
        if not data_list:
            print("数据加载失败，返回空列表")
            return []
        
        # 2. 创建Document对象
        documents = self.create_documents(data_list)
        if not documents:
            print("文档创建失败，返回空列表")
            return []
        
        # 3. 分割文档
        split_documents = self.split_documents(documents)
        
        print("数据处理完成")
        return split_documents

if __name__ == "__main__":
    # 测试数据加载
    loader = DataLoader()
    docs = loader.load_and_process_data()
    print(f"最终处理得到 {len(docs)} 个文档块")
    
    if docs:
        print("示例文档内容:")
        print(docs[0].page_content[:200] + "...")
        print("示例文档元数据:")
        print(docs[0].metadata)
