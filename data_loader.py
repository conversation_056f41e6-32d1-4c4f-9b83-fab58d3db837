"""
法律知识库数据加载和预处理模块
"""
import os
import json
import pandas as pd
from typing import List, Dict, Any
from modelscope.msdatasets import MsDataset
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LegalDataLoader:
    """法律数据加载器"""
    
    def __init__(self, data_dir: str = "./data"):
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
    
    def download_dataset(self) -> pd.DataFrame:
        """从魔搭下载法律知识库数据"""
        try:
            logger.info("开始下载法律知识库数据...")
            ds = MsDataset.load('kuailejingling/nongye', subset_name='default', split='train')
            
            # 转换为DataFrame
            data_list = []
            for item in ds:
                data_list.append(item)
            
            df = pd.DataFrame(data_list)
            logger.info(f"成功下载数据，共 {len(df)} 条记录")
            
            # 保存原始数据
            raw_data_path = os.path.join(self.data_dir, "raw_legal_data.json")
            df.to_json(raw_data_path, orient='records', ensure_ascii=False, indent=2)
            logger.info(f"原始数据已保存到: {raw_data_path}")
            
            return df
            
        except Exception as e:
            logger.error(f"下载数据时出错: {str(e)}")
            raise
    
    def preprocess_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """预处理法律数据"""
        logger.info("开始预处理数据...")
        
        processed_documents = []
        
        for idx, row in df.iterrows():
            try:
                # 根据数据结构调整字段名
                # 这里需要根据实际数据结构进行调整
                content = ""
                metadata = {}
                
                # 尝试不同的字段名组合
                possible_content_fields = ['content', 'text', 'question', 'answer', 'description']
                possible_title_fields = ['title', 'name', 'subject', 'topic']
                
                # 提取内容
                for field in possible_content_fields:
                    if field in row and pd.notna(row[field]):
                        if content:
                            content += "\n\n"
                        content += str(row[field])
                
                # 提取标题
                title = ""
                for field in possible_title_fields:
                    if field in row and pd.notna(row[field]):
                        title = str(row[field])
                        break
                
                # 如果没有找到内容，跳过这条记录
                if not content.strip():
                    continue
                
                # 构建元数据
                metadata = {
                    'source': 'kuailejingling/nongye',
                    'index': idx,
                    'title': title,
                }
                
                # 添加其他字段到元数据
                for key, value in row.items():
                    if key not in possible_content_fields and pd.notna(value):
                        metadata[key] = str(value)
                
                processed_documents.append({
                    'content': content,
                    'metadata': metadata
                })
                
            except Exception as e:
                logger.warning(f"处理第 {idx} 条记录时出错: {str(e)}")
                continue
        
        logger.info(f"预处理完成，共处理 {len(processed_documents)} 条有效记录")
        
        # 保存预处理后的数据
        processed_data_path = os.path.join(self.data_dir, "processed_legal_data.json")
        with open(processed_data_path, 'w', encoding='utf-8') as f:
            json.dump(processed_documents, f, ensure_ascii=False, indent=2)
        logger.info(f"预处理数据已保存到: {processed_data_path}")
        
        return processed_documents
    
    def load_processed_data(self) -> List[Dict[str, Any]]:
        """加载已预处理的数据"""
        processed_data_path = os.path.join(self.data_dir, "processed_legal_data.json")
        
        if os.path.exists(processed_data_path):
            logger.info("加载已预处理的数据...")
            with open(processed_data_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.info("未找到预处理数据，开始下载和处理...")
            df = self.download_dataset()
            return self.preprocess_data(df)

if __name__ == "__main__":
    # 测试数据加载
    loader = LegalDataLoader()
    documents = loader.load_processed_data()
    print(f"加载了 {len(documents)} 条法律文档")
    if documents:
        print("示例文档:")
        print(f"内容: {documents[0]['content'][:200]}...")
        print(f"元数据: {documents[0]['metadata']}")
