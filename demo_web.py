"""
法律知识小助手演示版Web界面
"""
import streamlit as st
import os
import logging
from typing import List, Dict, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置页面
st.set_page_config(
    page_title="法律知识小助手",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.WARNING)

class SimpleVectorStore:
    """简单的向量存储，使用TF-IDF"""
    
    def __init__(self):
        def chinese_tokenizer(text):
            import re
            tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result
        
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        self.documents = []
        self.vectors = None
    
    def add_documents(self, documents: List[Dict[str, Any]]):
        self.documents = documents
        texts = [doc['content'] for doc in documents]
        self.vectors = self.vectorizer.fit_transform(texts)
    
    def search(self, query: str, k: int = 3) -> List[Dict[str, Any]]:
        if self.vectors is None:
            return []
        
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(similarities[idx])
                })
        
        return results

class SimpleLegalAssistant:
    """简单的法律助手"""
    
    def __init__(self, vector_store: SimpleVectorStore):
        self.vector_store = vector_store
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            self.client = None
    
    def ask(self, question: str) -> Dict[str, Any]:
        relevant_docs = self.vector_store.search(question, k=3)
        
        if not relevant_docs:
            return {
                "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。",
                "source_documents": []
            }
        
        context = "\n\n".join([doc['content'] for doc in relevant_docs])
        
        if self.client:
            try:
                prompt = f"""你是一个专业的法律知识助手，能够回答涵盖农业法、劳动法、婚姻家庭法、房产法、消费者权益保护法、交通法、刑法、合同法等多个法律领域的问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

用户问题: {question}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 回答要准确、专业、易懂
3. 根据问题所属的法律领域，提供相应的专业解答
4. 如果涉及具体的法律程序或复杂案件，请建议用户咨询专业律师
5. 可以适当引用相关的法律条文或法规名称

回答:"""

                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1000,
                    temperature=0.7
                )
                
                answer = response.choices[0].message.content
                
            except Exception as e:
                answer = f"基于相关法律知识：{relevant_docs[0]['content'][:200]}... (API调用失败)"
        else:
            answer = f"根据相关法律规定：{relevant_docs[0]['content']}"
        
        return {
            "answer": answer,
            "source_documents": relevant_docs
        }

@st.cache_resource
def initialize_assistant():
    """初始化法律助手"""
    legal_documents = [
        # 农业法律
        {
            'content': '农业补贴是国家为了支持农业发展，提高农民收入而给予的资金支持。主要包括种粮直补、农资综合补贴、良种补贴、农机购置补贴等。这些补贴政策旨在调动农民种粮积极性，保障国家粮食安全。申请补贴需要提供相关证明材料，如土地承包证、种植证明等。',
            'metadata': {'source': 'policy', 'title': '农业补贴政策', 'type': '农业法', 'category': '政策解读'}
        },
        {
            'content': '土地承包经营权是农民依法对其承包土地享有的占有、使用和收益的权利。根据《农村土地承包法》，农民的土地承包经营权受法律保护，任何组织和个人不得侵犯。承包期内，发包方不得收回承包地。承包期为30年，到期后可以延长。',
            'metadata': {'source': 'law', 'title': '土地承包权保护', 'type': '农业法', 'category': '法律条文'}
        },

        # 劳动法
        {
            'content': '劳动合同是劳动者与用人单位确立劳动关系、明确双方权利和义务的协议。根据《劳动合同法》，用人单位应当与劳动者签订书面劳动合同。试用期不得超过六个月，同一用人单位与同一劳动者只能约定一次试用期。用人单位违法解除劳动合同的，应当支付赔偿金。',
            'metadata': {'source': 'law', 'title': '劳动合同法', 'type': '劳动法', 'category': '法律条文'}
        },
        {
            'content': '工资应当以货币形式按月支付给劳动者本人，不得克扣或者无故拖欠劳动者的工资。用人单位应当按照国家规定的标准支付加班费。法定节假日安排劳动者工作的，支付不低于工资的百分之三百的工资报酬。',
            'metadata': {'source': 'law', 'title': '工资支付规定', 'type': '劳动法', 'category': '法律条文'}
        },

        # 婚姻家庭法
        {
            'content': '婚姻自由是《民法典》确立的基本原则。禁止包办、买卖婚姻和其他干涉婚姻自由的行为。结婚年龄，男不得早于二十二周岁，女不得早于二十周岁。夫妻在婚姻关系存续期间所得的财产，为夫妻的共同财产，归夫妻共同所有。',
            'metadata': {'source': 'law', 'title': '婚姻法基本原则', 'type': '婚姻家庭法', 'category': '法律条文'}
        },
        {
            'content': '父母对未成年子女负有抚养、教育和保护的义务。成年子女对父母负有赡养、扶助和保护的义务。离婚后，不满两周岁的子女，以由母亲直接抚养为原则。已满两周岁的子女，父母双方对抚养问题协议不成的，由人民法院根据双方的具体情况，按照最有利于未成年子女的原则判决。',
            'metadata': {'source': 'law', 'title': '子女抚养规定', 'type': '婚姻家庭法', 'category': '法律条文'}
        },

        # 房产法
        {
            'content': '房屋买卖合同应当采用书面形式。房屋所有权的转移以登记为准。未经登记，不发生物权效力。购房者享有知情权，开发商应当如实告知房屋的基本情况。商品房买卖中，开发商延期交房超过约定期限的，购房者有权要求解除合同并要求赔偿损失。',
            'metadata': {'source': 'law', 'title': '房屋买卖法律规定', 'type': '房产法', 'category': '法律条文'}
        },
        {
            'content': '租赁期限不得超过二十年。超过二十年的，超过部分无效。租赁期间届满，当事人可以续订租赁合同；但是，约定的租赁期限自续订之日起不得超过二十年。出租人应当按照约定将租赁物交付承租人，并在租赁期间保持租赁物符合约定的用途。',
            'metadata': {'source': 'law', 'title': '房屋租赁法律规定', 'type': '房产法', 'category': '法律条文'}
        },

        # 消费者权益保护法
        {
            'content': '消费者享有安全权、知情权、选择权、公平交易权、求偿权、结社权、获得知识权、人格尊严与民族风俗习惯获得尊重权、监督权等九项权利。经营者提供商品或者服务有欺诈行为的，应当按照消费者的要求增加赔偿其受到的损失，增加赔偿的金额为消费者购买商品的价款或者接受服务的费用的三倍。',
            'metadata': {'source': 'law', 'title': '消费者权益保护', 'type': '消费者权益保护法', 'category': '法律条文'}
        },
        {
            'content': '网络购物七天无理由退货：消费者通过网络、电视、电话、邮购等方式购买商品，有权自收到商品之日起七日内退货，且无需说明理由。但下列商品除外：消费者定作的商品、鲜活易腐的商品、在线下载或者消费者拆封的音像制品、计算机软件等数字化商品、交付的报纸、期刊。',
            'metadata': {'source': 'law', 'title': '网购七天无理由退货', 'type': '消费者权益保护法', 'category': '法律条文'}
        },

        # 交通法
        {
            'content': '机动车发生交通事故造成人身伤亡、财产损失的，由保险公司在机动车第三者责任强制保险责任限额范围内予以赔偿；不足的部分，按照过错责任原则承担赔偿责任。酒后驾驶机动车的，处暂扣六个月机动车驾驶证，并处一千元以上二千元以下罚款。',
            'metadata': {'source': 'law', 'title': '交通事故责任', 'type': '交通法', 'category': '法律条文'}
        },

        # 刑法
        {
            'content': '故意杀人的，处死刑、无期徒刑或者十年以上有期徒刑；情节较轻的，处三年以上十年以下有期徒刑。故意伤害他人身体的，处三年以下有期徒刑、拘役或者管制。致人重伤的，处三年以上十年以下有期徒刑；致人死亡或者以特别残忍手段致人重伤造成严重残疾的，处十年以上有期徒刑、无期徒刑或者死刑。',
            'metadata': {'source': 'law', 'title': '故意伤害罪', 'type': '刑法', 'category': '法律条文'}
        },
        {
            'content': '盗窃公私财物，数额较大的，或者多次盗窃、入户盗窃、携带凶器盗窃、扒窃的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金；数额巨大或者有其他严重情节的，处三年以上十年以下有期徒刑，并处罚金。',
            'metadata': {'source': 'law', 'title': '盗窃罪', 'type': '刑法', 'category': '法律条文'}
        },

        # 合同法
        {
            'content': '当事人订立合同，应当具有相应的民事权利能力和民事行为能力。当事人依法享有自愿订立合同的权利，任何单位和个人不得非法干预。合同生效后，当事人不得因姓名、名称的变更或者法定代表人、负责人、承办人的变动而不履行合同义务。',
            'metadata': {'source': 'law', 'title': '合同订立原则', 'type': '合同法', 'category': '法律条文'}
        },
        {
            'content': '当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。当事人一方明确表示或者以自己的行为表明不履行合同义务的，对方可以在履行期限届满前请求其承担违约责任。',
            'metadata': {'source': 'law', 'title': '违约责任', 'type': '合同法', 'category': '法律条文'}
        }
    ]
    
    vector_store = SimpleVectorStore()
    vector_store.add_documents(legal_documents)
    assistant = SimpleLegalAssistant(vector_store)
    
    return assistant, len(legal_documents)

def main():
    """主函数"""
    # 页面标题
    st.title("⚖️ 法律知识小助手")
    st.markdown("---")
    
    # 侧边栏
    with st.sidebar:
        st.header("📋 功能介绍")
        st.markdown("""
        **法律知识小助手** 是基于专业法律知识库的AI助手，涵盖多个法律领域，为您提供全面的法律咨询服务。

        **主要功能领域:**
        - 🌾 **农业法**: 农业补贴、土地承包、农产品安全
        - 💼 **劳动法**: 劳动合同、工资支付、加班费
        - 💒 **婚姻家庭法**: 结婚离婚、子女抚养、财产分割
        - 🏠 **房产法**: 房屋买卖、租赁合同、产权登记
        - 🛒 **消费者权益**: 消费者权利、网购退货、欺诈赔偿
        - 🚗 **交通法**: 交通事故、酒驾处罚、责任认定
        - ⚖️ **刑法**: 犯罪构成、量刑标准、法律后果
        - 📄 **合同法**: 合同订立、违约责任、合同效力

        **使用说明:**
        1. 在下方输入框中输入您的法律问题
        2. 点击发送或按Enter键
        3. 查看AI助手的专业回答
        4. 可查看参考的法律文档
        """)
        
        st.markdown("---")
        
        if st.button("🗑️ 清除对话历史", use_container_width=True):
            st.session_state.messages = []
            st.rerun()
    
    # 初始化助手
    if 'assistant' not in st.session_state:
        with st.spinner("正在初始化法律知识小助手..."):
            assistant, doc_count = initialize_assistant()
            st.session_state.assistant = assistant
            st.session_state.doc_count = doc_count
        st.success(f"✅ 系统初始化完成！已加载 {doc_count} 条法律文档")
    
    # 初始化聊天历史
    if "messages" not in st.session_state:
        st.session_state.messages = []
        welcome_msg = """
        👋 您好！我是您的法律知识小助手。

        我可以帮您解答多个法律领域的问题，包括但不限于：

        🌾 **农业法**: 农业补贴、土地承包、农产品安全
        💼 **劳动法**: 劳动合同、工资支付、加班费
        💒 **婚姻家庭法**: 结婚离婚、子女抚养、财产分割
        🏠 **房产法**: 房屋买卖、租赁合同、产权登记
        🛒 **消费者权益**: 消费者权利、网购退货、欺诈赔偿
        🚗 **交通法**: 交通事故、酒驾处罚、责任认定
        ⚖️ **刑法**: 犯罪构成、量刑标准、法律后果
        📄 **合同法**: 合同订立、违约责任、合同效力

        请输入您的问题，我会基于专业的法律知识库为您提供准确的回答！
        """
        st.session_state.messages.append({"role": "assistant", "content": welcome_msg})
    
    # 显示聊天历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.write(message["content"])
            if message["role"] == "assistant" and "source_docs" in message:
                with st.expander(f"📚 参考文档 ({len(message['source_docs'])} 个)", expanded=False):
                    for i, doc in enumerate(message['source_docs'][:3], 1):
                        st.write(f"**文档 {i}:** {doc['metadata']['title']}")
                        st.write(doc['content'][:200] + "...")
                        st.divider()
    
    # 用户输入
    if prompt := st.chat_input("请输入您的法律问题..."):
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.write(prompt)
        
        with st.chat_message("assistant"):
            with st.spinner("正在查询相关法律信息..."):
                try:
                    result = st.session_state.assistant.ask(prompt)
                    answer = result['answer']
                    source_docs = result['source_documents']
                    
                    st.write(answer)
                    
                    if source_docs:
                        with st.expander(f"📚 参考文档 ({len(source_docs)} 个)", expanded=False):
                            for i, doc in enumerate(source_docs[:3], 1):
                                st.write(f"**文档 {i}:** {doc['metadata']['title']}")
                                st.write(doc['content'][:200] + "...")
                                st.divider()
                    
                    st.session_state.messages.append({
                        "role": "assistant", 
                        "content": answer,
                        "source_docs": source_docs
                    })
                    
                except Exception as e:
                    error_msg = f"抱歉，处理您的问题时出现了错误：{str(e)}"
                    st.error(error_msg)
                    st.session_state.messages.append({
                        "role": "assistant", 
                        "content": error_msg
                    })
    
    # 页面底部信息
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; font-size: 0.8em;'>
        ⚖️ 法律知识小助手 | 基于LangChain和DeepSeek API构建 | 
        ⚠️ 本助手提供的信息仅供参考，具体法律问题请咨询专业律师
        </div>
        """, 
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
