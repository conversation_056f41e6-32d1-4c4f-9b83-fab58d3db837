"""
法律知识小助手PPT生成器
使用python-pptx库创建专业的PowerPoint演示文稿
"""
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def create_legal_assistant_ppt():
    """创建法律知识小助手PPT"""

    # 创建演示文稿
    prs = Presentation()

    # 设置幻灯片尺寸为16:9
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    # 幻灯片1: 标题页
    slide1 = prs.slides.add_slide(prs.slide_layouts[0])  # 标题幻灯片布局
    title = slide1.shapes.title
    subtitle = slide1.placeholders[1]
    
    title.text = "⚖️ 法律知识小助手"
    title.text_frame.paragraphs[0].font.size = Pt(48)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    subtitle.text = "基于LangChain和DeepSeek API的智能法律咨询系统\n\n专业 • 全面 • 智能"
    subtitle.text_frame.paragraphs[0].font.size = Pt(24)
    subtitle.text_frame.paragraphs[0].font.color.rgb = RGBColor(102, 102, 102)
    
    # 幻灯片2: 项目概述
    slide2 = prs.slides.add_slide(prs.slide_layouts[1])  # 标题和内容布局
    title2 = slide2.shapes.title
    content2 = slide2.placeholders[1]
    
    title2.text = "📋 项目概述"
    title2.text_frame.paragraphs[0].font.size = Pt(36)
    title2.text_frame.paragraphs[0].font.bold = True
    title2.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content2.text = """🎯 项目目标
• 构建智能化的法律咨询助手
• 提供多领域专业法律知识服务
• 降低法律咨询门槛，提高效率

🔧 技术特点
• 基于LangChain框架构建
• 集成DeepSeek大语言模型
• 使用向量数据库进行知识检索
• 支持Web和命令行两种界面

💡 应用价值
• 24/7全天候法律咨询服务
• 快速准确的法律问题解答
• 降低法律服务成本
• 提升法律知识普及度"""
    
    # 设置内容字体
    for paragraph in content2.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片3: 功能范围
    slide3 = prs.slides.add_slide(prs.slide_layouts[1])
    title3 = slide3.shapes.title
    content3 = slide3.placeholders[1]
    
    title3.text = "🏛️ 功能范围 - 九大法律领域"
    title3.text_frame.paragraphs[0].font.size = Pt(32)
    title3.text_frame.paragraphs[0].font.bold = True
    title3.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content3.text = """🌾 农业法
• 农业补贴政策、土地承包权保护

💼 劳动法  
• 劳动合同、工资支付、合同解除

💒 婚姻家庭法
• 婚姻基本原则、子女抚养、夫妻财产

🏠 房产法
• 房屋买卖、租赁合同、产权登记

🛒 消费者权益保护法
• 消费者权利、网购退货、欺诈赔偿"""
    
    for paragraph in content3.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片4: 功能范围续
    slide4 = prs.slides.add_slide(prs.slide_layouts[1])
    title4 = slide4.shapes.title
    content4 = slide4.placeholders[1]
    
    title4.text = "🏛️ 功能范围 - 九大法律领域（续）"
    title4.text_frame.paragraphs[0].font.size = Pt(32)
    title4.text_frame.paragraphs[0].font.bold = True
    title4.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content4.text = """🚗 交通法
• 交通事故责任、酒驾处罚、责任认定

⚖️ 刑法
• 犯罪构成、量刑标准、法律后果

📄 合同法
• 合同订立、违约责任、合同效力

🔒 知识产权法
• 著作权法、商标法、专利保护

📊 知识库统计
• 总计23条专业法律文档
• 覆盖9个主要法律领域
• 持续更新和扩展"""
    
    for paragraph in content4.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片5: 技术架构
    slide5 = prs.slides.add_slide(prs.slide_layouts[1])
    title5 = slide5.shapes.title
    content5 = slide5.placeholders[1]
    
    title5.text = "🔧 技术架构"
    title5.text_frame.paragraphs[0].font.size = Pt(36)
    title5.text_frame.paragraphs[0].font.bold = True
    title5.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content5.text = """🧠 AI模型层
• DeepSeek Chat API - 大语言模型
• 专业法律问答能力
• 多轮对话支持

🔍 知识检索层
• TF-IDF向量化技术
• 中文法律文档优化
• 相似度匹配算法

📚 知识库层
• 结构化法律文档存储
• 多领域分类管理
• 元数据标注系统

🖥️ 应用层
• Streamlit Web界面
• 命令行交互界面
• RESTful API接口"""
    
    for paragraph in content5.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片6: 系统特点
    slide6 = prs.slides.add_slide(prs.slide_layouts[1])
    title6 = slide6.shapes.title
    content6 = slide6.placeholders[1]
    
    title6.text = "✨ 系统特点"
    title6.text_frame.paragraphs[0].font.size = Pt(36)
    title6.text_frame.paragraphs[0].font.bold = True
    title6.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content6.text = """🎯 智能化
• 自动识别法律问题类型
• 智能匹配相关法律条文
• 生成专业法律解答

🔄 交互性
• 支持多轮对话
• 上下文理解能力
• 个性化问答体验

📱 易用性
• 直观的Web界面设计
• 简洁的命令行操作
• 零学习成本使用

🛡️ 可靠性
• 基于权威法律文档
• 准确的法条引用
• 专业的法律建议提醒"""
    
    for paragraph in content6.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片7: 使用场景
    slide7 = prs.slides.add_slide(prs.slide_layouts[1])
    title7 = slide7.shapes.title
    content7 = slide7.placeholders[1]
    
    title7.text = "🎯 使用场景"
    title7.text_frame.paragraphs[0].font.size = Pt(36)
    title7.text_frame.paragraphs[0].font.bold = True
    title7.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content7.text = """👥 个人用户
• 日常法律问题咨询
• 权益保护指导
• 法律知识学习

🏢 企业用户
• 员工法律培训
• 合同条款解读
• 风险防范指导

🎓 教育机构
• 法律教学辅助
• 案例分析工具
• 学生自主学习

⚖️ 法律从业者
• 快速法条查询
• 案例参考资料
• 初步法律分析"""
    
    for paragraph in content7.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片8: 演示效果
    slide8 = prs.slides.add_slide(prs.slide_layouts[1])
    title8 = slide8.shapes.title
    content8 = slide8.placeholders[1]
    
    title8.text = "💻 演示效果"
    title8.text_frame.paragraphs[0].font.size = Pt(36)
    title8.text_frame.paragraphs[0].font.bold = True
    title8.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content8.text = """🌐 Web界面特点
• 类ChatGPT的对话体验
• 实时流式回答显示
• 参考文档展开查看
• 多领域问题智能识别

💬 问答示例
问题："试用期最长可以是多久？"
回答：根据《劳动合同法》规定，试用期最长不得超过6个月...

问题："法定结婚年龄是多少？"  
回答：根据《民法典》规定，男性不得早于22周岁，女性不得早于20周岁...

🔍 系统反馈
• 涉及法律领域标识
• 参考文档来源显示
• 专业律师咨询建议"""
    
    for paragraph in content8.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片9: 未来规划
    slide9 = prs.slides.add_slide(prs.slide_layouts[1])
    title9 = slide9.shapes.title
    content9 = slide9.placeholders[1]
    
    title9.text = "🚀 未来规划"
    title9.text_frame.paragraphs[0].font.size = Pt(36)
    title9.text_frame.paragraphs[0].font.bold = True
    title9.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    content9.text = """📈 功能扩展
• 增加更多法律领域覆盖
• 集成真实案例数据库
• 支持法律文书生成
• 添加语音交互功能

🔧 技术优化
• 升级到更先进的AI模型
• 优化向量检索算法
• 增强多语言支持
• 提升响应速度

🌍 应用拓展
• 开发移动端APP
• 集成微信小程序
• 提供API服务接口
• 支持私有化部署

🤝 合作机会
• 与律师事务所合作
• 接入法院系统
• 服务政府部门
• 面向教育市场"""
    
    for paragraph in content9.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.color.rgb = RGBColor(51, 51, 51)
    
    # 幻灯片10: 谢谢页面
    slide10 = prs.slides.add_slide(prs.slide_layouts[0])
    title10 = slide10.shapes.title
    subtitle10 = slide10.placeholders[1]
    
    title10.text = "谢谢观看！"
    title10.text_frame.paragraphs[0].font.size = Pt(48)
    title10.text_frame.paragraphs[0].font.bold = True
    title10.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 51, 102)
    
    subtitle10.text = """⚖️ 法律知识小助手
让法律咨询更智能、更便捷

🌐 Web界面: http://localhost:8501
📧 技术支持: 基于LangChain + DeepSeek API
🔧 开源项目: 持续更新和优化

感谢您的关注与支持！"""
    
    subtitle10.text_frame.paragraphs[0].font.size = Pt(20)
    subtitle10.text_frame.paragraphs[0].font.color.rgb = RGBColor(102, 102, 102)
    
    return prs

def main():
    """主函数"""
    print("🎨 正在生成法律知识小助手PPT...")
    
    # 创建PPT
    prs = create_legal_assistant_ppt()
    
    # 保存文件
    filename = "法律知识小助手演示.pptx"
    prs.save(filename)
    
    print(f"✅ PPT生成完成！")
    print(f"📁 文件名: {filename}")
    print(f"📊 幻灯片数量: {len(prs.slides)} 张")
    print(f"💡 内容包括: 项目概述、功能范围、技术架构、系统特点等")
    
    # 显示文件信息
    if os.path.exists(filename):
        file_size = os.path.getsize(filename)
        print(f"📏 文件大小: {file_size / 1024:.1f} KB")
    
    print(f"\n🎯 您可以使用PowerPoint或WPS打开此文件进行演示")

if __name__ == "__main__":
    main()
