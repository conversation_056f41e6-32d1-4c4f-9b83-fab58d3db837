#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准实验报告转换器
将标准化的Markdown实验报告转换为专业的Word文档格式
"""

import re
import os
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

class StandardReportConverter:
    def __init__(self):
        self.doc = Document()
        self.setup_styles()
        
    def setup_styles(self):
        """设置文档样式"""
        # 标题样式
        title_style = self.doc.styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = '黑体'
        title_font.size = Pt(18)
        title_font.bold = True
        title_font.color.rgb = RGBColor(0, 0, 0)
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_after = Pt(12)
        
        # 一级标题样式
        h1_style = self.doc.styles.add_style('CustomH1', WD_STYLE_TYPE.PARAGRAPH)
        h1_font = h1_style.font
        h1_font.name = '黑体'
        h1_font.size = Pt(16)
        h1_font.bold = True
        h1_font.color.rgb = RGBColor(0, 0, 0)
        h1_style.paragraph_format.space_before = Pt(12)
        h1_style.paragraph_format.space_after = Pt(6)
        
        # 二级标题样式
        h2_style = self.doc.styles.add_style('CustomH2', WD_STYLE_TYPE.PARAGRAPH)
        h2_font = h2_style.font
        h2_font.name = '黑体'
        h2_font.size = Pt(14)
        h2_font.bold = True
        h2_font.color.rgb = RGBColor(0, 0, 0)
        h2_style.paragraph_format.space_before = Pt(10)
        h2_style.paragraph_format.space_after = Pt(5)
        
        # 三级标题样式
        h3_style = self.doc.styles.add_style('CustomH3', WD_STYLE_TYPE.PARAGRAPH)
        h3_font = h3_style.font
        h3_font.name = '黑体'
        h3_font.size = Pt(12)
        h3_font.bold = True
        h3_font.color.rgb = RGBColor(0, 0, 0)
        h3_style.paragraph_format.space_before = Pt(8)
        h3_style.paragraph_format.space_after = Pt(4)
        
        # 正文样式
        body_style = self.doc.styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
        body_font = body_style.font
        body_font.name = '宋体'
        body_font.size = Pt(12)
        body_font.color.rgb = RGBColor(0, 0, 0)
        body_style.paragraph_format.line_spacing = 1.5
        body_style.paragraph_format.space_after = Pt(6)
        body_style.paragraph_format.first_line_indent = Inches(0.5)
        
        # 代码样式
        code_style = self.doc.styles.add_style('CustomCode', WD_STYLE_TYPE.PARAGRAPH)
        code_font = code_style.font
        code_font.name = 'Consolas'
        code_font.size = Pt(10)
        code_font.color.rgb = RGBColor(0, 0, 0)
        code_style.paragraph_format.left_indent = Inches(0.5)
        code_style.paragraph_format.space_after = Pt(6)
        
    def parse_markdown_content(self, content):
        """解析Markdown内容"""
        lines = content.split('\n')
        parsed_content = []
        current_section = None
        code_block = False
        code_content = []
        table_content = []
        in_table = False
        
        for line in lines:
            line = line.rstrip()
            
            # 处理代码块
            if line.startswith('```'):
                if code_block:
                    # 结束代码块
                    parsed_content.append({
                        'type': 'code',
                        'content': '\n'.join(code_content),
                        'language': current_section
                    })
                    code_content = []
                    code_block = False
                    current_section = None
                else:
                    # 开始代码块
                    code_block = True
                    current_section = line[3:].strip() if len(line) > 3 else 'text'
                continue
            
            if code_block:
                code_content.append(line)
                continue
            
            # 处理表格
            if '|' in line and line.strip().startswith('|'):
                if not in_table:
                    in_table = True
                    table_content = []
                table_content.append(line.strip())
                continue
            elif in_table and line.strip() == '':
                # 表格结束
                parsed_content.append({
                    'type': 'table',
                    'content': table_content
                })
                table_content = []
                in_table = False
                continue
            elif in_table:
                # 表格结束
                parsed_content.append({
                    'type': 'table',
                    'content': table_content
                })
                table_content = []
                in_table = False
                # 继续处理当前行
            
            # 处理标题
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('# ').strip()
                parsed_content.append({
                    'type': 'heading',
                    'level': level,
                    'content': title
                })
            # 处理列表
            elif line.strip().startswith(('- ', '* ', '+ ')):
                parsed_content.append({
                    'type': 'list_item',
                    'content': line.strip()[2:].strip()
                })
            elif re.match(r'^\d+\.\s', line.strip()):
                parsed_content.append({
                    'type': 'numbered_list',
                    'content': re.sub(r'^\d+\.\s', '', line.strip())
                })
            # 处理空行
            elif line.strip() == '':
                if parsed_content and parsed_content[-1]['type'] != 'empty':
                    parsed_content.append({'type': 'empty', 'content': ''})
            # 处理普通段落
            else:
                parsed_content.append({
                    'type': 'paragraph',
                    'content': line.strip()
                })
        
        # 处理未结束的表格
        if in_table and table_content:
            parsed_content.append({
                'type': 'table',
                'content': table_content
            })
        
        return parsed_content
    
    def add_table(self, table_data):
        """添加表格"""
        if not table_data:
            return
        
        # 解析表格数据
        rows = []
        for line in table_data:
            if '|' in line:
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                if cells and not all(cell.strip('-: ') == '' for cell in cells):
                    rows.append(cells)
        
        if not rows:
            return
        
        # 创建表格
        table = self.doc.add_table(rows=len(rows), cols=len(rows[0]))
        table.style = 'Table Grid'
        
        # 填充表格数据
        for i, row_data in enumerate(rows):
            for j, cell_data in enumerate(row_data):
                if j < len(table.rows[i].cells):
                    cell = table.rows[i].cells[j]
                    cell.text = cell_data
                    # 设置表头样式
                    if i == 0:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                run.font.bold = True
                                run.font.size = Pt(11)
        
        self.doc.add_paragraph()  # 表格后添加空行
    
    def convert_to_word(self, markdown_file, output_file):
        """转换Markdown文件为Word文档"""
        try:
            # 读取Markdown文件
            with open(markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析内容
            parsed_content = self.parse_markdown_content(content)
            
            # 转换为Word文档
            for item in parsed_content:
                if item['type'] == 'heading':
                    level = item['level']
                    content = item['content']
                    
                    if level == 1:
                        if '实验报告' in content and ('：' in content or ':' in content):
                            # 主标题
                            p = self.doc.add_paragraph(content, style='CustomTitle')
                        else:
                            p = self.doc.add_paragraph(content, style='CustomH1')
                    elif level == 2:
                        p = self.doc.add_paragraph(content, style='CustomH2')
                    elif level == 3:
                        p = self.doc.add_paragraph(content, style='CustomH3')
                    else:
                        p = self.doc.add_paragraph(content, style='CustomH3')
                
                elif item['type'] == 'paragraph':
                    if item['content']:
                        # 处理特殊格式
                        content = item['content']
                        if content.startswith('**') and content.endswith('**'):
                            # 粗体段落
                            p = self.doc.add_paragraph()
                            run = p.add_run(content[2:-2])
                            run.font.bold = True
                            run.font.size = Pt(12)
                        elif '：' in content or ':' in content:
                            # 可能是实验信息
                            p = self.doc.add_paragraph(content, style='CustomBody')
                            p.paragraph_format.first_line_indent = Inches(0)
                        else:
                            p = self.doc.add_paragraph(content, style='CustomBody')
                
                elif item['type'] == 'code':
                    # 添加代码块
                    p = self.doc.add_paragraph(item['content'], style='CustomCode')
                    # 设置代码块背景色
                    p.paragraph_format.left_indent = Inches(0.5)
                    p.paragraph_format.right_indent = Inches(0.5)
                
                elif item['type'] == 'table':
                    self.add_table(item['content'])
                
                elif item['type'] == 'list_item':
                    p = self.doc.add_paragraph(f"• {item['content']}", style='CustomBody')
                    p.paragraph_format.left_indent = Inches(0.5)
                    p.paragraph_format.first_line_indent = Inches(-0.25)
                
                elif item['type'] == 'numbered_list':
                    p = self.doc.add_paragraph(f"1. {item['content']}", style='CustomBody')
                    p.paragraph_format.left_indent = Inches(0.5)
                    p.paragraph_format.first_line_indent = Inches(-0.25)
                
                elif item['type'] == 'empty':
                    self.doc.add_paragraph()
            
            # 保存文档
            self.doc.save(output_file)
            print(f"✅ 成功转换: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
            return False

def main():
    """主函数"""
    converter = StandardReportConverter()
    
    # 转换实验报告3
    print("开始转换实验报告3...")
    success1 = converter.convert_to_word(
        '实验报告3_智能法律文书生成系统研究.md',
        '标准实验报告3_智能法律文书生成系统研究.docx'
    )
    
    # 重新初始化转换器
    converter = StandardReportConverter()
    
    # 转换实验报告4
    print("开始转换实验报告4...")
    success2 = converter.convert_to_word(
        '实验报告4_法律AI系统安全性与可信度评估.md',
        '标准实验报告4_法律AI系统安全性与可信度评估.docx'
    )
    
    if success1 and success2:
        print("\n🎉 所有实验报告转换完成！")
        print("生成的文件:")
        print("- 标准实验报告3_智能法律文书生成系统研究.docx")
        print("- 标准实验报告4_法律AI系统安全性与可信度评估.docx")
    else:
        print("\n⚠️ 部分转换失败，请检查错误信息")

if __name__ == "__main__":
    main()
