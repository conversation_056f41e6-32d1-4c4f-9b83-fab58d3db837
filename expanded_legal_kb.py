"""
扩展的法律知识库数据
包含多个法律领域的专业知识
"""

EXPANDED_LEGAL_DOCUMENTS = [
    # 农业法
    {
        'content': '农业补贴是国家为了支持农业发展，提高农民收入而给予的资金支持。主要包括种粮直补、农资综合补贴、良种补贴、农机购置补贴等。这些补贴政策旨在调动农民种粮积极性，保障国家粮食安全。申请补贴需要提供相关证明材料，如土地承包证、种植证明等。',
        'metadata': {'source': 'policy', 'title': '农业补贴政策', 'type': '农业法', 'category': '政策解读'}
    },
    {
        'content': '土地承包经营权是农民依法对其承包土地享有的占有、使用和收益的权利。根据《农村土地承包法》，农民的土地承包经营权受法律保护，任何组织和个人不得侵犯。承包期内，发包方不得收回承包地。承包期为30年，到期后可以延长。',
        'metadata': {'source': 'law', 'title': '土地承包权保护', 'type': '农业法', 'category': '法律条文'}
    },
    
    # 劳动法
    {
        'content': '劳动合同是劳动者与用人单位确立劳动关系、明确双方权利和义务的协议。根据《劳动合同法》，用人单位应当与劳动者签订书面劳动合同。试用期不得超过六个月，同一用人单位与同一劳动者只能约定一次试用期。用人单位违法解除劳动合同的，应当支付赔偿金。',
        'metadata': {'source': 'law', 'title': '劳动合同法', 'type': '劳动法', 'category': '法律条文'}
    },
    {
        'content': '工资应当以货币形式按月支付给劳动者本人，不得克扣或者无故拖欠劳动者的工资。用人单位应当按照国家规定的标准支付加班费。法定节假日安排劳动者工作的，支付不低于工资的百分之三百的工资报酬。平时加班支付不低于工资的百分之一百五十，休息日加班支付不低于工资的百分之二百。',
        'metadata': {'source': 'law', 'title': '工资支付规定', 'type': '劳动法', 'category': '法律条文'}
    },
    {
        'content': '用人单位单方面解除劳动合同需要符合法定条件。劳动者有下列情形之一的，用人单位可以解除劳动合同：在试用期间被证明不符合录用条件的；严重违反用人单位的规章制度的；严重失职，营私舞弊，给用人单位造成重大损害的；被依法追究刑事责任的。',
        'metadata': {'source': 'law', 'title': '劳动合同解除', 'type': '劳动法', 'category': '法律条文'}
    },
    
    # 婚姻家庭法
    {
        'content': '婚姻自由是《民法典》确立的基本原则。禁止包办、买卖婚姻和其他干涉婚姻自由的行为。结婚年龄，男不得早于二十二周岁，女不得早于二十周岁。夫妻在婚姻关系存续期间所得的财产，为夫妻的共同财产，归夫妻共同所有。',
        'metadata': {'source': 'law', 'title': '婚姻法基本原则', 'type': '婚姻家庭法', 'category': '法律条文'}
    },
    {
        'content': '父母对未成年子女负有抚养、教育和保护的义务。成年子女对父母负有赡养、扶助和保护的义务。离婚后，不满两周岁的子女，以由母亲直接抚养为原则。已满两周岁的子女，父母双方对抚养问题协议不成的，由人民法院根据双方的具体情况，按照最有利于未成年子女的原则判决。',
        'metadata': {'source': 'law', 'title': '子女抚养规定', 'type': '婚姻家庭法', 'category': '法律条文'}
    },
    {
        'content': '夫妻共同财产包括：工资、奖金、劳务报酬；生产、经营、投资的收益；知识产权的收益；继承或者受赠的财产，但遗嘱或者赠与合同中确定只归一方的财产除外；其他应当归共同所有的财产。夫妻个人财产包括：一方的婚前财产；一方因受到人身损害获得的赔偿或者补偿等。',
        'metadata': {'source': 'law', 'title': '夫妻财产制度', 'type': '婚姻家庭法', 'category': '法律条文'}
    },
    
    # 房产法
    {
        'content': '房屋买卖合同应当采用书面形式。房屋所有权的转移以登记为准。未经登记，不发生物权效力。购房者享有知情权，开发商应当如实告知房屋的基本情况。商品房买卖中，开发商延期交房超过约定期限的，购房者有权要求解除合同并要求赔偿损失。',
        'metadata': {'source': 'law', 'title': '房屋买卖法律规定', 'type': '房产法', 'category': '法律条文'}
    },
    {
        'content': '租赁期限不得超过二十年。超过二十年的，超过部分无效。租赁期间届满，当事人可以续订租赁合同；但是，约定的租赁期限自续订之日起不得超过二十年。出租人应当按照约定将租赁物交付承租人，并在租赁期间保持租赁物符合约定的用途。',
        'metadata': {'source': 'law', 'title': '房屋租赁法律规定', 'type': '房产法', 'category': '法律条文'}
    },
    {
        'content': '房屋买卖中的"五证二书"是指：国有土地使用证、建设用地规划许可证、建设工程规划许可证、建筑工程施工许可证、商品房预售许可证、住宅质量保证书、住宅使用说明书。购房者在购买商品房时应当查验开发商是否具备这些证书。',
        'metadata': {'source': 'law', 'title': '房屋买卖五证二书', 'type': '房产法', 'category': '法律条文'}
    },
    
    # 消费者权益保护法
    {
        'content': '消费者享有安全权、知情权、选择权、公平交易权、求偿权、结社权、获得知识权、人格尊严与民族风俗习惯获得尊重权、监督权等九项权利。经营者提供商品或者服务有欺诈行为的，应当按照消费者的要求增加赔偿其受到的损失，增加赔偿的金额为消费者购买商品的价款或者接受服务的费用的三倍。',
        'metadata': {'source': 'law', 'title': '消费者权益保护', 'type': '消费者权益保护法', 'category': '法律条文'}
    },
    {
        'content': '网络购物七天无理由退货：消费者通过网络、电视、电话、邮购等方式购买商品，有权自收到商品之日起七日内退货，且无需说明理由。但下列商品除外：消费者定作的商品、鲜活易腐的商品、在线下载或者消费者拆封的音像制品、计算机软件等数字化商品、交付的报纸、期刊。',
        'metadata': {'source': 'law', 'title': '网购七天无理由退货', 'type': '消费者权益保护法', 'category': '法律条文'}
    },
    
    # 交通法
    {
        'content': '机动车发生交通事故造成人身伤亡、财产损失的，由保险公司在机动车第三者责任强制保险责任限额范围内予以赔偿；不足的部分，按照过错责任原则承担赔偿责任。酒后驾驶机动车的，处暂扣六个月机动车驾驶证，并处一千元以上二千元以下罚款。醉酒驾驶机动车的，吊销机动车驾驶证，五年内不得重新取得机动车驾驶证。',
        'metadata': {'source': 'law', 'title': '交通事故责任', 'type': '交通法', 'category': '法律条文'}
    },
    {
        'content': '机动车超速行驶的处罚：超过规定时速10%以下的，给予警告；超过规定时速10%以上不足50%的，处200元罚款；超过规定时速50%以上不足100%的，处500元罚款，可以并处吊销机动车驾驶证；超过规定时速100%以上的，处2000元罚款，并处吊销机动车驾驶证。',
        'metadata': {'source': 'law', 'title': '超速行驶处罚', 'type': '交通法', 'category': '法律条文'}
    },
    
    # 刑法
    {
        'content': '故意杀人的，处死刑、无期徒刑或者十年以上有期徒刑；情节较轻的，处三年以上十年以下有期徒刑。故意伤害他人身体的，处三年以下有期徒刑、拘役或者管制。致人重伤的，处三年以上十年以下有期徒刑；致人死亡或者以特别残忍手段致人重伤造成严重残疾的，处十年以上有期徒刑、无期徒刑或者死刑。',
        'metadata': {'source': 'law', 'title': '故意伤害罪', 'type': '刑法', 'category': '法律条文'}
    },
    {
        'content': '盗窃公私财物，数额较大的，或者多次盗窃、入户盗窃、携带凶器盗窃、扒窃的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金；数额巨大或者有其他严重情节的，处三年以上十年以下有期徒刑，并处罚金；数额特别巨大或者有其他特别严重情节的，处十年以上有期徒刑或者无期徒刑，并处罚金或者没收财产。',
        'metadata': {'source': 'law', 'title': '盗窃罪', 'type': '刑法', 'category': '法律条文'}
    },
    {
        'content': '诈骗公私财物，数额较大的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金；数额巨大或者有其他严重情节的，处三年以上十年以下有期徒刑，并处罚金；数额特别巨大或者有其他特别严重情节的，处十年以上有期徒刑或者无期徒刑，并处罚金或者没收财产。',
        'metadata': {'source': 'law', 'title': '诈骗罪', 'type': '刑法', 'category': '法律条文'}
    },
    
    # 合同法
    {
        'content': '当事人订立合同，应当具有相应的民事权利能力和民事行为能力。当事人依法享有自愿订立合同的权利，任何单位和个人不得非法干预。合同生效后，当事人不得因姓名、名称的变更或者法定代表人、负责人、承办人的变动而不履行合同义务。',
        'metadata': {'source': 'law', 'title': '合同订立原则', 'type': '合同法', 'category': '法律条文'}
    },
    {
        'content': '当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。当事人一方明确表示或者以自己的行为表明不履行合同义务的，对方可以在履行期限届满前请求其承担违约责任。',
        'metadata': {'source': 'law', 'title': '违约责任', 'type': '合同法', 'category': '法律条文'}
    },
    {
        'content': '合同无效的情形包括：一方以欺诈、胁迫的手段订立合同，损害国家利益；恶意串通，损害国家、集体或者第三人利益；以合法形式掩盖非法目的；损害社会公共利益；违反法律、行政法规的强制性规定。无效的合同自始没有法律约束力。',
        'metadata': {'source': 'law', 'title': '合同无效情形', 'type': '合同法', 'category': '法律条文'}
    },
    
    # 知识产权法
    {
        'content': '著作权是指文学、艺术和科学作品的作者对其作品享有的权利。著作权包括人身权和财产权。人身权包括发表权、署名权、修改权、保护作品完整权。财产权包括复制权、发行权、出租权、展览权、表演权、放映权、广播权、信息网络传播权、摄制权、改编权、翻译权、汇编权等。',
        'metadata': {'source': 'law', 'title': '著作权法', 'type': '知识产权法', 'category': '法律条文'}
    },
    {
        'content': '商标是用来区别一个经营者的品牌或服务和其他经营者的商品或服务的标记。商标权人享有商标专用权，受法律保护。注册商标的有效期为十年，自核准注册之日起计算。注册商标有效期满，需要继续使用的，商标注册人应当在期满前十二个月内按照规定办理续展手续。',
        'metadata': {'source': 'law', 'title': '商标法', 'type': '知识产权法', 'category': '法律条文'}
    }
]

def get_legal_documents_by_type(legal_type=None):
    """根据法律类型获取文档"""
    if legal_type is None:
        return EXPANDED_LEGAL_DOCUMENTS
    
    return [doc for doc in EXPANDED_LEGAL_DOCUMENTS if doc['metadata']['type'] == legal_type]

def get_all_legal_types():
    """获取所有法律类型"""
    types = set()
    for doc in EXPANDED_LEGAL_DOCUMENTS:
        types.add(doc['metadata']['type'])
    return sorted(list(types))

if __name__ == "__main__":
    print("扩展法律知识库统计:")
    print(f"总文档数: {len(EXPANDED_LEGAL_DOCUMENTS)}")
    print("\n各法律领域文档数量:")
    for legal_type in get_all_legal_types():
        count = len(get_legal_documents_by_type(legal_type))
        print(f"  {legal_type}: {count} 个文档")
