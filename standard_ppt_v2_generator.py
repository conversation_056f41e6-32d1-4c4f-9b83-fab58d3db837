#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准版PPT生成器 V2 - 系统功能与应用实践
使用项目真实代码截图的专业演示文稿
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
import os

class StandardPPTV2Generator:
    def __init__(self):
        self.prs = Presentation()
        self.setup_slide_master()
    
    def setup_slide_master(self):
        """设置幻灯片母版样式"""
        # 设置幻灯片尺寸为16:9
        self.prs.slide_width = Inches(13.33)
        self.prs.slide_height = Inches(7.5)
    
    def add_title_slide(self):
        """添加标题页"""
        slide_layout = self.prs.slide_layouts[0]
        slide = self.prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        subtitle = slide.placeholders[1]
        
        title.text = "智能法律知识助手系统"
        title.text_frame.paragraphs[0].font.size = Pt(44)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 102, 51)
        title.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
        
        subtitle.text = "系统功能与应用实践详解\n\n多模态交互界面与智能问答实现"
        for paragraph in subtitle.text_frame.paragraphs:
            paragraph.font.size = Pt(20)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.alignment = PP_ALIGN.CENTER
    
    def add_features_overview_slide(self):
        """添加功能概述页"""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "系统功能特色"
        title.text_frame.paragraphs[0].font.size = Pt(36)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 102, 51)
        
        content.text = """🎯 核心功能模块
• 智能法律问答：基于DeepSeek的专业回答
• 文档检索系统：TF-IDF高效匹配
• 多轮对话支持：上下文理解与记忆
• 法律领域识别：9大专业领域覆盖

🖥️ 交互界面
• Web界面：Streamlit响应式设计
• CLI命令行：开发者友好的终端交互
• 实时反馈：即时响应用户查询
• 错误处理：友好的异常提示

📚 知识库管理
• 结构化数据存储
• 向量化索引建立
• 动态内容更新
• 高效检索算法

🔧 技术集成
• LangChain框架集成
• DeepSeek API调用
• 中文文本处理优化
• 模块化架构设计"""
        
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(16)
    
    def add_code_slide_with_screenshot(self, title, screenshot_path, explanation, slide_number):
        """添加带真实代码截图的幻灯片"""
        slide_layout = self.prs.slide_layouts[6]  # 空白布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 添加标题
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.2), Inches(12), Inches(0.8))
        title_frame = title_box.text_frame
        title_frame.text = title
        title_para = title_frame.paragraphs[0]
        title_para.font.size = Pt(28)
        title_para.font.bold = True
        title_para.font.color.rgb = RGBColor(0, 102, 51)
        title_para.alignment = PP_ALIGN.CENTER
        
        # 左侧：代码截图
        try:
            if os.path.exists(screenshot_path):
                slide.shapes.add_picture(screenshot_path, Inches(0.3), Inches(1.1), Inches(7.5), Inches(5.5))
                print(f"✅ 成功添加截图: {os.path.basename(screenshot_path)}")
            else:
                print(f"⚠️ 截图文件不存在: {screenshot_path}")
                # 添加占位符
                placeholder = slide.shapes.add_textbox(Inches(0.3), Inches(1.1), Inches(7.5), Inches(5.5))
                placeholder_frame = placeholder.text_frame
                placeholder_frame.text = f"代码截图\n{os.path.basename(screenshot_path)}\n(文件不存在)"
                placeholder.fill.solid()
                placeholder.fill.fore_color.rgb = RGBColor(240, 240, 240)
                placeholder.line.color.rgb = RGBColor(200, 200, 200)
        except Exception as e:
            print(f"❌ 添加截图失败: {e}")
            # 添加错误占位符
            placeholder = slide.shapes.add_textbox(Inches(0.3), Inches(1.1), Inches(7.5), Inches(5.5))
            placeholder_frame = placeholder.text_frame
            placeholder_frame.text = f"代码截图加载失败\n{os.path.basename(screenshot_path)}\n错误: {str(e)}"
            placeholder.fill.solid()
            placeholder.fill.fore_color.rgb = RGBColor(255, 240, 240)
            placeholder.line.color.rgb = RGBColor(255, 200, 200)
        
        # 右侧：功能解释
        explanation_box = slide.shapes.add_textbox(Inches(8.2), Inches(1.1), Inches(4.8), Inches(5.5))
        explanation_frame = explanation_box.text_frame
        explanation_frame.text = explanation
        explanation_frame.word_wrap = True
        
        # 设置解释文本样式
        for paragraph in explanation_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.font.color.rgb = RGBColor(51, 51, 51)
            paragraph.space_after = Pt(8)
        
        # 添加幻灯片编号
        slide_num_box = slide.shapes.add_textbox(Inches(12), Inches(6.8), Inches(1), Inches(0.4))
        slide_num_frame = slide_num_box.text_frame
        slide_num_frame.text = f"第 {slide_number} 页"
        slide_num_frame.paragraphs[0].font.size = Pt(12)
        slide_num_frame.paragraphs[0].font.color.rgb = RGBColor(128, 128, 128)
        slide_num_frame.paragraphs[0].alignment = PP_ALIGN.RIGHT
    
    def add_application_slide(self):
        """添加应用场景页"""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "应用场景与实践价值"
        title.text_frame.paragraphs[0].font.size = Pt(36)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = RGBColor(0, 102, 51)
        
        content.text = """🏢 企业应用场景
• 法务部门：合同审查、风险评估
• 人力资源：劳动法咨询、政策解读
• 财务部门：税法查询、合规检查
• 管理层：法律风险预警、决策支持

👥 个人用户场景
• 普通公民：日常法律问题咨询
• 学生群体：法律知识学习辅助
• 创业者：商业法律风险评估
• 专业人士：法律条文快速查询

📊 实践效果
• 咨询效率提升：传统人工咨询 vs AI助手
  - 响应时间：24小时 → 3秒
  - 可用性：工作时间 → 24/7
  - 成本：高昂费用 → 免费使用
  - 覆盖面：单一专业 → 9大领域

🎯 社会价值
• 降低法律服务门槛
• 提高法律知识普及率
• 减少法律纠纷发生
• 促进法治社会建设"""
        
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(16)
    
    def generate_presentation(self):
        """生成完整的演示文稿"""
        print("🎯 开始生成标准版PPT V2...")
        
        # 1. 标题页
        print("📝 添加标题页...")
        self.add_title_slide()
        
        # 2. 功能概述页
        print("📋 添加功能概述页...")
        self.add_features_overview_slide()
        
        # 3-7. 功能实现页（使用真实代码截图）
        code_slides = [
            ("向量存储管理 - 高效数据索引", "real_code_6_vector.png", 
             """💾 存储特色

• 向量化数据管理
• 高效索引建立
• 快速检索支持
• 内存优化策略

🔍 检索优势
• 毫秒级响应时间
• 高精度匹配算法
• 可扩展存储架构
• 并发访问支持"""),
            
            ("知识库结构 - 法律数据组织", "real_code_7_kb.png",
             """📚 数据架构

• 结构化知识组织
• 多层级分类体系
• 标准化数据格式
• 动态内容更新

⚡ 管理优势
• 高效的数据访问
• 灵活的扩展能力
• 一致的数据质量
• 智能的内容索引"""),
            
            ("CLI界面实现 - 开发者工具", "real_code_8_cli.png",
             """🖥️ 命令行特色

• 简洁的交互设计
• 丰富的命令支持
• 实时结果显示
• 开发者友好

🛠️ 功能亮点
• 批量查询处理
• 脚本化操作支持
• 调试信息输出
• 性能监控工具"""),
            
            ("系统初始化流程 - 启动优化", "real_code_1_init.png",
             """🚀 启动流程

• 快速系统初始化
• 资源预加载机制
• 错误恢复策略
• 状态监控系统

💡 优化策略
• 延迟加载技术
• 缓存预热机制
• 并行初始化
• 健康检查系统"""),
            
            ("智能分词引擎 - 中文处理", "real_code_2_tokenizer.png",
             """🔤 分词技术

• 专业术语识别
• 上下文理解
• 语义分析支持
• 多语言兼容

🎯 处理优势
• 高准确率分词
• 法律术语优化
• 实时处理能力
• 可定制词典""")
        ]
        
        for i, (title, screenshot, explanation) in enumerate(code_slides, 3):
            print(f"💻 添加功能页 {i}: {title}")
            self.add_code_slide_with_screenshot(title, screenshot, explanation, i)
        
        # 8. 应用场景页
        print("📊 添加应用场景页...")
        self.add_application_slide()
        
        # 保存文件
        filename = "法律AI助手系统_标准版V2_功能应用.pptx"
        self.prs.save(filename)
        print(f"✅ PPT生成完成: {filename}")
        print(f"📄 总计 {len(self.prs.slides)} 张幻灯片")
        
        return filename

def main():
    """主函数"""
    generator = StandardPPTV2Generator()
    filename = generator.generate_presentation()
    
    # 尝试打开生成的PPT
    try:
        os.startfile(filename)
        print(f"🎉 PPT已自动打开: {filename}")
    except Exception as e:
        print(f"⚠️ 无法自动打开PPT: {e}")
        print(f"请手动打开文件: {filename}")

if __name__ == "__main__":
    main()
