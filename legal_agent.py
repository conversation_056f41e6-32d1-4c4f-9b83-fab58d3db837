"""
法律知识小助手智能体
"""
import os
import logging
from typing import List, Dict, Any, Optional
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferMemory
import openai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepSeekLLM(LLM):
    """DeepSeek API的LangChain包装器"""

    api_key: str = ""
    base_url: str = ""
    client: openai.OpenAI = None

    def __init__(self):
        super().__init__()
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')

        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")

        # 配置OpenAI客户端使用DeepSeek API
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    @property
    def _llm_type(self) -> str:
        return "deepseek"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7,
                stop=stop
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {str(e)}")
            return f"抱歉，我遇到了一些技术问题：{str(e)}"

class LegalAssistant:
    """法律知识小助手"""
    
    def __init__(self, vectorstore):
        self.vectorstore = vectorstore
        self.llm = DeepSeekLLM()
        
        # 创建检索器
        self.retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 5}
        )
        
        # 创建记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        # 创建提示模板
        self.prompt_template = PromptTemplate(
            input_variables=["context", "question", "chat_history"],
            template="""你是一个专业的法律知识助手，专门回答农业相关的法律问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

历史对话:
{chat_history}

用户问题: {question}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 如果知识库中没有相关信息，请诚实地说明
3. 回答要准确、专业、易懂
4. 可以适当引用具体的法律条文或案例
5. 如果问题涉及具体的法律建议，请提醒用户咨询专业律师

回答:"""
        )
        
        # 创建QA链
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=self.retriever,
            return_source_documents=True,
            chain_type_kwargs={
                "prompt": self.prompt_template,
                "memory": self.memory
            }
        )
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答用户问题"""
        try:
            logger.info(f"用户问题: {question}")
            
            # 获取相关文档
            relevant_docs = self.retriever.get_relevant_documents(question)
            
            # 构建上下文
            context = "\n\n".join([doc.page_content for doc in relevant_docs])
            
            # 获取历史对话
            chat_history = self.memory.chat_memory.messages
            history_text = ""
            for msg in chat_history[-6:]:  # 只保留最近3轮对话
                if hasattr(msg, 'content'):
                    role = "用户" if msg.__class__.__name__ == "HumanMessage" else "助手"
                    history_text += f"{role}: {msg.content}\n"
            
            # 构建完整提示
            full_prompt = self.prompt_template.format(
                context=context,
                question=question,
                chat_history=history_text
            )
            
            # 调用LLM
            response = self.llm._call(full_prompt)
            
            # 保存到记忆
            self.memory.chat_memory.add_user_message(question)
            self.memory.chat_memory.add_ai_message(response)
            
            result = {
                "answer": response,
                "source_documents": [
                    {
                        "content": doc.page_content[:300] + "..." if len(doc.page_content) > 300 else doc.page_content,
                        "metadata": doc.metadata
                    }
                    for doc in relevant_docs
                ]
            }
            
            logger.info("回答生成完成")
            return result
            
        except Exception as e:
            logger.error(f"回答问题时出错: {str(e)}")
            return {
                "answer": f"抱歉，我在处理您的问题时遇到了错误：{str(e)}",
                "source_documents": []
            }
    
    def clear_memory(self):
        """清除对话记忆"""
        self.memory.clear()
        logger.info("对话记忆已清除")

if __name__ == "__main__":
    # 测试法律助手
    from vector_store import VectorStoreManager
    from data_loader import LegalDataLoader
    
    # 加载数据和向量数据库
    loader = LegalDataLoader()
    processed_data = loader.load_processed_data()
    
    vector_manager = VectorStoreManager()
    vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
    
    # 创建法律助手
    assistant = LegalAssistant(vectorstore)
    
    # 测试问答
    test_questions = [
        "农业补贴政策有哪些？",
        "农民土地承包权有什么保护？",
        "农产品质量安全法的主要内容是什么？"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        result = assistant.ask(question)
        print(f"回答: {result['answer']}")
        print(f"参考文档数量: {len(result['source_documents'])}")
