import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")
    
    # 模型配置
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
    LLM_MODEL = os.getenv("LLM_MODEL", "deepseek-chat")
    
    # 向量数据库配置
    VECTOR_DB_PATH = os.getenv("VECTOR_DB_PATH", "./vector_db")
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", "200"))
    
    # Streamlit配置
    STREAMLIT_PORT = int(os.getenv("STREAMLIT_PORT", "8501"))
    
    # ModelScope数据集配置
    DATASET_NAME = "kuailejingling/nongye"
    DATASET_SUBSET = "default"
    DATASET_SPLIT = "train"
    
    @classmethod
    def validate(cls):
        """验证配置"""
        if not cls.DEEPSEEK_API_KEY:
            raise ValueError("DEEPSEEK_API_KEY 未设置")
        
        # 创建必要的目录
        os.makedirs(cls.VECTOR_DB_PATH, exist_ok=True)
        
        return True
