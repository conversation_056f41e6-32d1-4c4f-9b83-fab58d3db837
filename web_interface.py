"""
法律知识小助手Web界面 (Streamlit)
"""
import streamlit as st
import logging
from typing import Dict, Any
from data_loader import LegalDataLoader
from vector_store import VectorStoreManager
from legal_agent import LegalAssistant

# 配置页面
st.set_page_config(
    page_title="法律知识小助手",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@st.cache_resource
def initialize_assistant():
    """初始化法律助手（使用缓存避免重复初始化）"""
    try:
        with st.spinner("正在初始化法律知识小助手..."):
            # 加载数据
            loader = LegalDataLoader()
            processed_data = loader.load_processed_data()
            
            # 初始化向量数据库
            vector_manager = VectorStoreManager()
            vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
            
            # 创建法律助手
            assistant = LegalAssistant(vectorstore)
            
            return assistant, len(processed_data)
    except Exception as e:
        st.error(f"初始化失败: {str(e)}")
        logger.error(f"初始化失败: {str(e)}")
        return None, 0

def display_chat_message(role: str, content: str, source_docs: list = None):
    """显示聊天消息"""
    with st.chat_message(role):
        st.write(content)
        
        if source_docs and role == "assistant":
            with st.expander(f"📚 参考文档 ({len(source_docs)} 个)", expanded=False):
                for i, doc in enumerate(source_docs[:3], 1):
                    st.write(f"**文档 {i}:**")
                    st.write(doc['content'])
                    if 'title' in doc['metadata'] and doc['metadata']['title']:
                        st.write(f"*标题: {doc['metadata']['title']}*")
                    st.divider()

def main():
    """主函数"""
    # 页面标题
    st.title("⚖️ 法律知识小助手")
    st.markdown("---")
    
    # 侧边栏
    with st.sidebar:
        st.header("📋 功能介绍")
        st.markdown("""
        **法律知识小助手** 是基于专业法律知识库的AI助手，专门回答农业相关的法律问题。
        
        **主要功能:**
        - 🌾 农业补贴政策咨询
        - 🏡 土地承包权保护
        - 🛡️ 农产品质量安全
        - 🤝 农业合作社法律
        - 🔄 农村土地流转
        - 📋 农业保险相关法律
        
        **使用说明:**
        1. 在下方输入框中输入您的法律问题
        2. 点击发送或按Enter键
        3. 查看AI助手的专业回答
        4. 可查看参考的法律文档
        """)
        
        st.markdown("---")
        
        # 清除对话按钮
        if st.button("🗑️ 清除对话历史", use_container_width=True):
            st.session_state.messages = []
            if 'assistant' in st.session_state:
                st.session_state.assistant.clear_memory()
            st.rerun()
    
    # 初始化助手
    if 'assistant' not in st.session_state:
        assistant, doc_count = initialize_assistant()
        if assistant:
            st.session_state.assistant = assistant
            st.session_state.doc_count = doc_count
            st.success(f"✅ 系统初始化完成！已加载 {doc_count} 条法律文档")
        else:
            st.error("❌ 系统初始化失败，请刷新页面重试")
            return
    
    # 初始化聊天历史
    if "messages" not in st.session_state:
        st.session_state.messages = []
        # 添加欢迎消息
        welcome_msg = """
        👋 您好！我是您的法律知识小助手。
        
        我可以帮您解答农业相关的法律问题，包括但不限于：
        - 农业补贴政策
        - 土地承包权保护  
        - 农产品质量安全
        - 农业合作社法律
        - 农村土地流转
        - 农业保险相关法律
        
        请输入您的问题，我会基于专业的法律知识库为您提供准确的回答！
        """
        st.session_state.messages.append({"role": "assistant", "content": welcome_msg})
    
    # 显示聊天历史
    for message in st.session_state.messages:
        display_chat_message(
            message["role"], 
            message["content"], 
            message.get("source_docs", [])
        )
    
    # 用户输入
    if prompt := st.chat_input("请输入您的法律问题..."):
        # 添加用户消息到历史
        st.session_state.messages.append({"role": "user", "content": prompt})
        display_chat_message("user", prompt)
        
        # 获取助手回答
        with st.chat_message("assistant"):
            with st.spinner("正在查询相关法律信息..."):
                try:
                    result = st.session_state.assistant.ask(prompt)
                    answer = result['answer']
                    source_docs = result['source_documents']
                    
                    # 显示回答
                    st.write(answer)
                    
                    # 显示参考文档
                    if source_docs:
                        with st.expander(f"📚 参考文档 ({len(source_docs)} 个)", expanded=False):
                            for i, doc in enumerate(source_docs[:3], 1):
                                st.write(f"**文档 {i}:**")
                                st.write(doc['content'])
                                if 'title' in doc['metadata'] and doc['metadata']['title']:
                                    st.write(f"*标题: {doc['metadata']['title']}*")
                                st.divider()
                    
                    # 添加助手消息到历史
                    st.session_state.messages.append({
                        "role": "assistant", 
                        "content": answer,
                        "source_docs": source_docs
                    })
                    
                except Exception as e:
                    error_msg = f"抱歉，处理您的问题时出现了错误：{str(e)}"
                    st.error(error_msg)
                    st.session_state.messages.append({
                        "role": "assistant", 
                        "content": error_msg
                    })
    
    # 页面底部信息
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; font-size: 0.8em;'>
        ⚖️ 法律知识小助手 | 基于LangChain和DeepSeek API构建 | 
        ⚠️ 本助手提供的信息仅供参考，具体法律问题请咨询专业律师
        </div>
        """, 
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
