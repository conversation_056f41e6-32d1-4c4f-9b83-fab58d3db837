"""
法律知识小助手启动脚本
"""
import sys
import argparse
import subprocess
import os

def run_cli():
    """运行命令行界面"""
    print("启动命令行界面...")
    from cli_interface import main
    main()

def run_web():
    """运行Web界面"""
    print("启动Web界面...")
    print("Web界面将在浏览器中打开: http://localhost:8501")
    subprocess.run([sys.executable, "-m", "streamlit", "run", "web_interface.py"])

def setup_environment():
    """设置环境"""
    print("检查环境配置...")
    
    # 检查.env文件
    if not os.path.exists('.env'):
        print("❌ 未找到.env文件，请确保已创建并配置了DeepSeek API密钥")
        return False
    
    # 检查必要的包
    try:
        import langchain
        import modelscope
        import chromadb
        import streamlit
        print("✅ 所有必要的包都已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少必要的包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    parser = argparse.ArgumentParser(description="法律知识小助手")
    parser.add_argument(
        "--mode", 
        choices=["cli", "web"], 
        default="cli",
        help="运行模式: cli (命令行) 或 web (网页界面)"
    )
    parser.add_argument(
        "--setup", 
        action="store_true",
        help="仅检查环境配置"
    )
    
    args = parser.parse_args()
    
    # 检查环境
    if not setup_environment():
        sys.exit(1)
    
    if args.setup:
        print("✅ 环境配置检查完成")
        return
    
    # 运行相应模式
    if args.mode == "cli":
        run_cli()
    elif args.mode == "web":
        run_web()

if __name__ == "__main__":
    main()
