"""
向量数据库管理模块
"""
import os
import pickle
import logging
from typing import List, Dict, Any
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.docstore.document import Document
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorStoreManager:
    """向量数据库管理器"""

    def __init__(self):
        self.chunk_size = int(os.getenv('CHUNK_SIZE', 1000))
        self.chunk_overlap = int(os.getenv('CHUNK_OVERLAP', 200))
        self.persist_directory = os.getenv('FAISS_PERSIST_DIRECTORY', './faiss_db')
        self.embedding_model_name = os.getenv('EMBEDDING_MODEL', 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')

        # 确保持久化目录存在
        os.makedirs(self.persist_directory, exist_ok=True)

        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
        )

        # 初始化嵌入模型
        logger.info(f"初始化嵌入模型: {self.embedding_model_name}")
        self.embeddings = HuggingFaceEmbeddings(
            model_name=self.embedding_model_name,
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )

        self.vectorstore = None
    
    def create_documents(self, processed_data: List[Dict[str, Any]]) -> List[Document]:
        """将处理后的数据转换为LangChain文档格式"""
        logger.info("创建LangChain文档...")
        
        documents = []
        for item in processed_data:
            doc = Document(
                page_content=item['content'],
                metadata=item['metadata']
            )
            documents.append(doc)
        
        logger.info(f"创建了 {len(documents)} 个文档")
        return documents
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为小块"""
        logger.info("分割文档...")
        
        split_docs = self.text_splitter.split_documents(documents)
        
        logger.info(f"文档分割完成，共 {len(split_docs)} 个文档块")
        return split_docs
    
    def create_vectorstore(self, documents: List[Document]) -> FAISS:
        """创建向量数据库"""
        logger.info("创建向量数据库...")

        # 分割文档
        split_docs = self.split_documents(documents)

        # 创建向量数据库
        self.vectorstore = FAISS.from_documents(
            documents=split_docs,
            embedding=self.embeddings
        )

        # 持久化
        index_path = os.path.join(self.persist_directory, "faiss_index")
        self.vectorstore.save_local(index_path)
        logger.info(f"向量数据库创建完成，存储在: {index_path}")

        return self.vectorstore

    def load_vectorstore(self) -> FAISS:
        """加载已存在的向量数据库"""
        index_path = os.path.join(self.persist_directory, "faiss_index")
        if os.path.exists(index_path):
            logger.info("加载已存在的向量数据库...")
            self.vectorstore = FAISS.load_local(
                index_path,
                self.embeddings,
                allow_dangerous_deserialization=True
            )
            return self.vectorstore
        else:
            logger.warning("向量数据库不存在，需要先创建")
            return None

    def get_or_create_vectorstore(self, processed_data: List[Dict[str, Any]] = None) -> FAISS:
        """获取或创建向量数据库"""
        # 尝试加载已存在的向量数据库
        vectorstore = self.load_vectorstore()
        
        if vectorstore is None:
            if processed_data is None:
                raise ValueError("向量数据库不存在且未提供数据来创建新的数据库")
            
            # 创建新的向量数据库
            documents = self.create_documents(processed_data)
            vectorstore = self.create_vectorstore(documents)
        
        return vectorstore
    
    def search_similar(self, query: str, k: int = 5) -> List[Document]:
        """搜索相似文档"""
        if self.vectorstore is None:
            raise ValueError("向量数据库未初始化")
        
        return self.vectorstore.similarity_search(query, k=k)
    
    def search_with_score(self, query: str, k: int = 5) -> List[tuple]:
        """搜索相似文档并返回相似度分数"""
        if self.vectorstore is None:
            raise ValueError("向量数据库未初始化")
        
        return self.vectorstore.similarity_search_with_score(query, k=k)

if __name__ == "__main__":
    # 测试向量数据库
    from data_loader import LegalDataLoader
    
    # 加载数据
    loader = LegalDataLoader()
    processed_data = loader.load_processed_data()
    
    # 创建向量数据库
    vector_manager = VectorStoreManager()
    vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
    
    # 测试搜索
    test_query = "农业法律"
    results = vector_manager.search_similar(test_query, k=3)
    
    print(f"搜索查询: {test_query}")
    print(f"找到 {len(results)} 个相关文档:")
    for i, doc in enumerate(results):
        print(f"\n文档 {i+1}:")
        print(f"内容: {doc.page_content[:200]}...")
        print(f"元数据: {doc.metadata}")
