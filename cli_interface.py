"""
法律知识小助手命令行界面
"""
import os
import sys
import logging
from typing import Optional
from data_loader import LegalDataLoader
from vector_store import VectorStoreManager
from legal_agent import LegalAssistant

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LegalAssistantCLI:
    """法律助手命令行界面"""
    
    def __init__(self):
        self.assistant: Optional[LegalAssistant] = None
        self.is_initialized = False
    
    def initialize(self):
        """初始化系统"""
        try:
            print("🏛️  法律知识小助手初始化中...")
            print("=" * 50)
            
            # 1. 加载数据
            print("📚 加载法律知识库数据...")
            loader = LegalDataLoader()
            processed_data = loader.load_processed_data()
            print(f"✅ 成功加载 {len(processed_data)} 条法律文档")
            
            # 2. 初始化向量数据库
            print("🔍 初始化向量数据库...")
            vector_manager = VectorStoreManager()
            vectorstore = vector_manager.get_or_create_vectorstore(processed_data)
            print("✅ 向量数据库初始化完成")
            
            # 3. 创建法律助手
            print("🤖 创建法律知识助手...")
            self.assistant = LegalAssistant(vectorstore)
            print("✅ 法律知识助手创建完成")
            
            self.is_initialized = True
            print("=" * 50)
            print("🎉 系统初始化完成！")
            
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            print(f"❌ 初始化失败: {str(e)}")
            sys.exit(1)
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("\n" + "=" * 60)
        print("🏛️  欢迎使用法律知识小助手！")
        print("=" * 60)
        print("📋 功能说明:")
        print("   • 回答农业相关的法律问题")
        print("   • 提供法律条文和政策解读")
        print("   • 基于专业法律知识库进行回答")
        print("\n💡 使用提示:")
        print("   • 输入您的法律问题，我会为您详细解答")
        print("   • 输入 'clear' 清除对话历史")
        print("   • 输入 'help' 查看帮助信息")
        print("   • 输入 'quit' 或 'exit' 退出程序")
        print("=" * 60)
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 帮助信息:")
        print("=" * 40)
        print("🔹 支持的问题类型:")
        print("   • 农业补贴政策")
        print("   • 土地承包权保护")
        print("   • 农产品质量安全")
        print("   • 农业合作社法律")
        print("   • 农村土地流转")
        print("   • 农业保险相关法律")
        print("\n🔹 命令说明:")
        print("   clear  - 清除对话历史")
        print("   help   - 显示此帮助信息")
        print("   quit   - 退出程序")
        print("   exit   - 退出程序")
        print("=" * 40)
    
    def format_answer(self, result: dict):
        """格式化回答显示"""
        print("\n🤖 法律助手回答:")
        print("-" * 40)
        print(result['answer'])
        
        if result['source_documents']:
            print(f"\n📚 参考文档 ({len(result['source_documents'])} 个):")
            print("-" * 40)
            for i, doc in enumerate(result['source_documents'][:3], 1):  # 只显示前3个
                print(f"📄 文档 {i}:")
                print(f"   内容: {doc['content']}")
                if 'title' in doc['metadata'] and doc['metadata']['title']:
                    print(f"   标题: {doc['metadata']['title']}")
                print()
    
    def run(self):
        """运行命令行界面"""
        # 初始化系统
        if not self.is_initialized:
            self.initialize()
        
        # 显示欢迎信息
        self.show_welcome()
        
        # 主循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n💬 请输入您的法律问题: ").strip()
                
                # 处理空输入
                if not user_input:
                    continue
                
                # 处理命令
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("\n👋 感谢使用法律知识小助手，再见！")
                    break
                
                elif user_input.lower() in ['help', '帮助']:
                    self.show_help()
                    continue
                
                elif user_input.lower() in ['clear', '清除']:
                    self.assistant.clear_memory()
                    print("✅ 对话历史已清除")
                    continue
                
                # 处理法律问题
                print("\n🔍 正在查询相关法律信息...")
                result = self.assistant.ask(user_input)
                self.format_answer(result)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                logger.error(f"处理用户输入时出错: {str(e)}")
                print(f"\n❌ 处理您的问题时出现错误: {str(e)}")
                print("请重试或联系技术支持。")

def main():
    """主函数"""
    cli = LegalAssistantCLI()
    cli.run()

if __name__ == "__main__":
    main()
