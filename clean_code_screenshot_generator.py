#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clean Code Screenshot Generator
Generate code screenshots without Chinese characters to avoid font issues
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
import re

class CleanCodeScreenshotGenerator:
    def __init__(self):
        # Use monospace font for code display
        plt.rcParams['font.family'] = 'monospace'
        plt.rcParams['axes.unicode_minus'] = False
        
        # VS Code Dark theme colors
        self.colors = {
            'background': '#1e1e1e',
            'text': '#d4d4d4',
            'keyword': '#569cd6',
            'string': '#ce9178',
            'comment': '#6a9955',
            'function': '#dcdcaa',
            'class': '#4ec9b0',
            'number': '#b5cea8',
            'border': '#404040'
        }
    
    def clean_chinese_text(self, text):
        """Remove or replace Chinese characters with English equivalents"""
        # Remove Chinese characters completely
        cleaned_text = re.sub(r'[\u4e00-\u9fff]+', '', text)
        
        # Replace common Chinese comments with English
        replacements = {
            '# ': '# ',
            '"""': '"""',
            "'''": "'''",
            '# 初始化': '# Initialize',
            '# 配置': '# Configure', 
            '# 加载': '# Load',
            '# 建立': '# Build',
            '# 实现': '# Implementation',
            '# 计算': '# Calculate',
            '# 检索': '# Retrieve',
            '# 筛选': '# Filter',
            '# 识别': '# Identify',
            '# 构建': '# Build',
            '# 调用': '# Call',
            '# 响应': '# Response',
            '# 优化': '# Optimize',
            '# 设计': '# Design',
            '# 布局': '# Layout',
            '# 交互': '# Interaction',
            '# 管理': '# Management',
            '# 索引': '# Index',
            '# 组织': '# Organization',
            '# 分类': '# Classification',
            '# 支持': '# Support',
            '智能法律知识助手系统': 'Legal AI Assistant System',
            '基于': 'Based on',
            '多领域': 'Multi-domain',
            '法律咨询': 'Legal Consultation',
            '文档向量化': 'Document Vectorization',
            '存储和检索': 'Storage and Retrieval',
            '中文分词': 'Chinese Tokenization',
            '实现': 'Implementation'
        }
        
        for chinese, english in replacements.items():
            cleaned_text = cleaned_text.replace(chinese, english)
        
        # Remove any remaining Chinese characters and special symbols
        cleaned_text = re.sub(r'[^\x00-\x7F]+', '', cleaned_text)
        
        return cleaned_text
    
    def read_code_from_file(self, filename, start_line=1, end_line=None):
        """Read code from file and clean Chinese characters"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if end_line is None:
                end_line = len(lines)
            
            # Extract specified lines
            code_lines = lines[start_line-1:end_line]
            
            # Add line numbers and clean Chinese text
            numbered_lines = []
            for i, line in enumerate(code_lines, start_line):
                clean_line = self.clean_chinese_text(line.rstrip())
                if clean_line.strip():  # Only add non-empty lines
                    numbered_lines.append(f"{i:3d}  {clean_line}")
            
            return '\n'.join(numbered_lines)
        except Exception as e:
            return f"# Error reading file: {e}"
    
    def create_code_screenshot(self, code_text, title, filename, width=14, height=10):
        """Create clean code screenshot without Chinese characters"""
        fig, ax = plt.subplots(figsize=(width, height))
        
        # Set background color
        fig.patch.set_facecolor(self.colors['background'])
        ax.set_facecolor(self.colors['background'])
        
        # Remove axes
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # Add title (clean English only)
        clean_title = self.clean_chinese_text(title)
        ax.text(0.5, 0.95, clean_title, fontsize=18, fontweight='bold', 
                color='white', ha='center', va='top', family='monospace')
        
        # Add code text box
        code_box = patches.FancyBboxPatch(
            (0.02, 0.05), 0.96, 0.85,
            boxstyle="round,pad=0.02",
            facecolor=self.colors['background'],
            edgecolor=self.colors['border'],
            linewidth=2
        )
        ax.add_patch(code_box)
        
        # Add clean code text
        clean_code = self.clean_chinese_text(code_text)
        ax.text(0.05, 0.88, clean_code, fontsize=10, 
                fontfamily='monospace', color=self.colors['text'], 
                ha='left', va='top', linespacing=1.3)
        
        # Save screenshot
        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight', 
                   facecolor=self.colors['background'], edgecolor='none')
        plt.close()
        
        print(f"✅ Clean code screenshot saved: {filename}")
    
    def generate_all_clean_screenshots(self):
        """Generate all clean code screenshots"""
        
        print("🎯 Generating clean code screenshots...")
        
        # 1. System initialization code
        print("📸 Generating system initialization screenshot...")
        init_code = self.read_code_from_file("comprehensive_legal_assistant.py", 1, 25)
        self.create_code_screenshot(init_code, "System Initialization - ComprehensiveLegalAssistant", 
                                  "clean_code_1_init.png")
        
        # 2. Chinese tokenizer implementation
        print("📸 Generating tokenizer implementation screenshot...")
        tokenizer_code = self.read_code_from_file("comprehensive_legal_assistant.py", 26, 44)
        self.create_code_screenshot(tokenizer_code, "Chinese Tokenizer Implementation", 
                                  "clean_code_2_tokenizer.png")
        
        # 3. Document search algorithm
        print("📸 Generating document search algorithm screenshot...")
        search_code = self.read_code_from_file("comprehensive_legal_assistant.py", 68, 86)
        self.create_code_screenshot(search_code, "Document Search Algorithm - TF-IDF", 
                                  "clean_code_3_search.png")
        
        # 4. AI question-answering logic
        print("📸 Generating AI QA logic screenshot...")
        qa_code = self.read_code_from_file("comprehensive_legal_assistant.py", 88, 120)
        self.create_code_screenshot(qa_code, "AI Question-Answering Logic", 
                                  "clean_code_4_ask.png")
        
        # 5. Web interface initialization
        print("📸 Generating web interface screenshot...")
        web_code = self.read_code_from_file("web_interface.py", 23, 43)
        self.create_code_screenshot(web_code, "Web Interface Initialization - Streamlit", 
                                  "clean_code_5_web_init.png")
        
        # 6. Vector storage management
        print("📸 Generating vector storage screenshot...")
        vector_code = self.read_code_from_file("vector_store.py", 1, 25)
        self.create_code_screenshot(vector_code, "Vector Storage Management", 
                                  "clean_code_6_vector.png")
        
        # 7. Knowledge base structure
        print("📸 Generating knowledge base structure screenshot...")
        kb_code = self.read_code_from_file("expanded_legal_kb.py", 1, 30)
        self.create_code_screenshot(kb_code, "Legal Knowledge Base Structure", 
                                  "clean_code_7_kb.png")
        
        # 8. CLI interface implementation
        print("📸 Generating CLI interface screenshot...")
        cli_code = self.read_code_from_file("cli_interface.py", 1, 25)
        self.create_code_screenshot(cli_code, "CLI Interface Implementation", 
                                  "clean_code_8_cli.png")
        
        print("\n🎉 All clean code screenshots generated successfully!")
        print("Generated screenshot files:")
        for i in range(1, 9):
            print(f"  • clean_code_{i}_*.png")

def main():
    """Main function"""
    generator = CleanCodeScreenshotGenerator()
    generator.generate_all_clean_screenshots()

if __name__ == "__main__":
    main()
