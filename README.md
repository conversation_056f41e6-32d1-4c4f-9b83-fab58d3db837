# ⚖️ 法律知识小助手

基于LangChain和DeepSeek API构建的智能法律咨询助手，专门回答农业相关的法律问题。

## 🌟 功能特点

- 🌾 **专业农业法律知识**: 基于专业法律知识库，提供准确的农业法律咨询
- 🤖 **智能问答**: 使用DeepSeek大语言模型，理解复杂法律问题
- 🔍 **语义检索**: 基于向量数据库的相似度搜索，快速找到相关法律条文
- 💬 **对话记忆**: 支持多轮对话，理解上下文
- 🖥️ **多种界面**: 支持命令行和Web界面两种使用方式

## 📋 支持的法律领域

- 农业补贴政策
- 土地承包权保护
- 农产品质量安全
- 农业合作社法律
- 农村土地流转
- 农业保险相关法律

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装Python 3.8+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置API密钥

项目已预配置了DeepSeek API密钥，如需修改请编辑`.env`文件：

```env
DEEPSEEK_API_KEY=your_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

### 4. 运行程序

#### 命令行界面
```bash
python run.py --mode cli
```

#### Web界面
```bash
python run.py --mode web
```

Web界面将在浏览器中自动打开: http://localhost:8501

## 📁 项目结构

```
├── data_loader.py          # 数据加载和预处理
├── vector_store.py         # 向量数据库管理
├── legal_agent.py          # 法律助手核心逻辑
├── cli_interface.py        # 命令行界面
├── web_interface.py        # Web界面 (Streamlit)
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖包列表
├── .env                    # 环境配置
└── README.md              # 项目说明
```

## 🔧 技术架构

- **数据源**: 魔搭平台法律知识库 (`kuailejingling/nongye`)
- **向量数据库**: ChromaDB
- **嵌入模型**: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
- **大语言模型**: DeepSeek Chat API
- **框架**: LangChain
- **Web界面**: Streamlit

## 💡 使用示例

### 命令行界面示例

```
💬 请输入您的法律问题: 农业补贴政策有哪些？

🤖 法律助手回答:
----------------------------------------
根据相关法律法规，农业补贴政策主要包括以下几个方面：

1. **种粮直补**: 对种植粮食作物的农民给予直接补贴
2. **农资综合补贴**: 补贴农民购买化肥、柴油等农业生产资料
3. **良种补贴**: 对使用优良品种的农民给予补贴
4. **农机购置补贴**: 补贴农民购买农业机械设备
...

📚 参考文档 (3 个):
----------------------------------------
📄 文档 1:
   内容: 农业补贴是国家为了支持农业发展...
   标题: 农业补贴政策解读
```

### Web界面特点

- 🎨 美观的聊天界面
- 📱 响应式设计，支持移动端
- 📚 可展开查看参考文档
- 🗑️ 一键清除对话历史
- ⚡ 实时流式回答

## ⚠️ 重要提醒

本助手提供的法律信息仅供参考，不构成正式的法律建议。对于具体的法律问题，建议咨询专业律师。

## 🛠️ 开发说明

### 自定义数据源

如需使用其他法律知识库，请修改`data_loader.py`中的数据源配置：

```python
ds = MsDataset.load('your_dataset_name', subset_name='default', split='train')
```

### 调整模型参数

可在`.env`文件中调整以下参数：

```env
CHUNK_SIZE=1000              # 文档分块大小
CHUNK_OVERLAP=200            # 分块重叠大小
EMBEDDING_MODEL=model_name   # 嵌入模型名称
```

## 📞 技术支持

如遇到问题，请检查：

1. Python版本是否为3.8+
2. 所有依赖包是否正确安装
3. DeepSeek API密钥是否有效
4. 网络连接是否正常

## 📄 许可证

本项目仅供学习和研究使用。
