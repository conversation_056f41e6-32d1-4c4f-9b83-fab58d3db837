"""
增强版法律知识小助手 - 支持多轮对话和连续提问
"""
import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import openai
from dotenv import load_dotenv
from expanded_legal_kb import EXPANDED_LEGAL_DOCUMENTS

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

class ConversationManager:
    """对话管理器 - 处理多轮对话逻辑"""
    
    def __init__(self, max_history=20):
        self.conversation_history = []
        self.max_history = max_history
        self.session_start_time = datetime.now()
    
    def add_exchange(self, question: str, answer: str, context_docs: List[Dict] = None):
        """添加一轮对话"""
        exchange = {
            'timestamp': datetime.now(),
            'question': question,
            'answer': answer,
            'context_docs': context_docs or [],
            'turn_id': len(self.conversation_history) + 1
        }
        
        self.conversation_history.append(exchange)
        
        # 保持历史记录在限制范围内
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
    
    def get_recent_context(self, turns: int = 5) -> str:
        """获取最近几轮对话的上下文"""
        if not self.conversation_history:
            return ""
        
        recent_exchanges = self.conversation_history[-turns:]
        context_parts = []
        
        for exchange in recent_exchanges:
            context_parts.append(f"用户: {exchange['question']}")
            context_parts.append(f"助手: {exchange['answer'][:200]}...")
        
        return "\n".join(context_parts)
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        if not self.conversation_history:
            return {"total_turns": 0, "topics": [], "duration": "0分钟"}
        
        total_turns = len(self.conversation_history)
        duration = datetime.now() - self.session_start_time
        duration_minutes = int(duration.total_seconds() / 60)
        
        # 简单的主题提取（基于关键词）
        all_questions = " ".join([ex['question'] for ex in self.conversation_history])
        topics = []
        topic_keywords = {
            "劳动法": ["试用期", "工资", "加班", "劳动合同", "解除"],
            "婚姻法": ["结婚", "离婚", "抚养", "财产", "年龄"],
            "房产法": ["买房", "租房", "房屋", "产权", "合同"],
            "消费者权益": ["退货", "维权", "消费", "欺诈", "质量"],
            "交通法": ["酒驾", "事故", "违章", "处罚", "责任"],
            "刑法": ["犯罪", "盗窃", "诈骗", "伤害", "量刑"]
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in all_questions for keyword in keywords):
                topics.append(topic)
        
        return {
            "total_turns": total_turns,
            "topics": topics,
            "duration": f"{duration_minutes}分钟",
            "start_time": self.session_start_time.strftime("%H:%M")
        }
    
    def clear_history(self):
        """清除对话历史"""
        self.conversation_history = []
        self.session_start_time = datetime.now()

class EnhancedLegalAssistant:
    """增强版法律助手 - 支持多轮对话"""
    
    def __init__(self):
        # 初始化对话管理器
        self.conversation_manager = ConversationManager()
        
        # 初始化中文分词器
        def chinese_tokenizer(text):
            import re
            tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result
        
        # 初始化向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        
        # 加载法律文档
        self.documents = EXPANDED_LEGAL_DOCUMENTS
        texts = [doc['content'] for doc in self.documents]
        self.vectors = self.vectorizer.fit_transform(texts)
        
        # 初始化AI客户端
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            self.client = None
            logger.warning("未配置DeepSeek API密钥")
        
        print(f"✅ 增强版法律知识库初始化完成！")
        print(f"📚 已加载 {len(self.documents)} 条法律文档")
        print(f"🔄 支持多轮对话和连续提问")
    
    def search_documents(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关法律文档"""
        if self.vectors is None:
            return []
        
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(similarities[idx])
                })
        
        return results
    
    def detect_follow_up_question(self, question: str) -> bool:
        """检测是否为追问"""
        follow_up_indicators = [
            "那么", "那", "还有", "另外", "此外", "补充", "追问",
            "刚才", "上面", "之前", "前面", "刚刚", "刚说",
            "详细", "具体", "举例", "比如", "例如",
            "还", "再", "继续", "进一步", "更多"
        ]
        
        return any(indicator in question for indicator in follow_up_indicators)
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答法律问题 - 支持多轮对话"""
        # 检测是否为追问
        is_follow_up = self.detect_follow_up_question(question)
        
        # 搜索相关文档
        relevant_docs = self.search_documents(question, k=5)
        
        if not relevant_docs:
            response = {
                "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。请尝试换个表达方式或咨询专业律师。",
                "source_documents": [],
                "legal_areas": [],
                "is_follow_up": is_follow_up,
                "conversation_turn": len(self.conversation_manager.conversation_history) + 1
            }
            self.conversation_manager.add_exchange(question, response["answer"])
            return response
        
        # 分析涉及的法律领域
        legal_areas = list(set([doc['metadata']['type'] for doc in relevant_docs]))
        
        # 构建上下文
        context = "\n\n".join([f"【{doc['metadata']['type']} - {doc['metadata']['title']}】\n{doc['content']}" 
                              for doc in relevant_docs])
        
        # 获取对话历史
        chat_history = self.conversation_manager.get_recent_context(3)
        
        if self.client:
            try:
                # 构建提示词
                if is_follow_up and chat_history:
                    prompt = f"""你是一个专业的法律知识助手，正在进行多轮对话。用户刚才提出了追问，请结合之前的对话历史来回答。

之前的对话:
{chat_history}

相关法律知识:
{context}

用户的追问: {question}

涉及的法律领域: {', '.join(legal_areas)}

请注意:
1. 这是一个追问，请结合之前的对话内容来理解和回答
2. 请基于提供的法律知识库内容进行回答
3. 回答要准确、专业、易懂
4. 可以适当引用相关的法律条文或法规名称
5. 如果涉及具体的法律程序或复杂案件，请建议用户咨询专业律师
6. 可以说"根据刚才提到的..."或"结合您之前的问题..."来体现连续性

回答:"""
                else:
                    prompt = f"""你是一个专业的法律知识助手，能够回答涵盖多个法律领域的问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

用户问题: {question}

涉及的法律领域: {', '.join(legal_areas)}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 回答要准确、专业、易懂
3. 根据问题所属的法律领域，提供相应的专业解答
4. 可以适当引用相关的法律条文或法规名称
5. 如果涉及具体的法律程序或复杂案件，请建议用户咨询专业律师
6. 如果涉及多个法律领域，请分别说明

回答:"""

                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1500,
                    temperature=0.7
                )
                
                answer = response.choices[0].message.content
                
            except Exception as e:
                logger.error(f"API调用失败: {str(e)}")
                answer = f"基于相关法律知识：{relevant_docs[0]['content'][:300]}... \n\n(注：API调用失败，显示部分相关内容，建议咨询专业律师获取完整解答)"
        else:
            # 无API时的简单回答
            if is_follow_up and chat_history:
                answer = f"结合您之前的问题，根据{relevant_docs[0]['metadata']['type']}相关规定：\n\n{relevant_docs[0]['content']}\n\n建议咨询专业律师获取更详细的法律建议。"
            else:
                answer = f"根据{relevant_docs[0]['metadata']['type']}相关规定：\n\n{relevant_docs[0]['content']}\n\n建议咨询专业律师获取更详细的法律建议。"
        
        result = {
            "answer": answer,
            "source_documents": relevant_docs,
            "legal_areas": legal_areas,
            "is_follow_up": is_follow_up,
            "conversation_turn": len(self.conversation_manager.conversation_history) + 1
        }
        
        # 保存到对话历史
        self.conversation_manager.add_exchange(question, answer, relevant_docs)
        
        return result
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        return self.conversation_manager.get_conversation_summary()
    
    def clear_conversation(self):
        """清除对话历史"""
        self.conversation_manager.clear_history()
        print("✅ 对话历史已清除，开始新的对话")
    
    def show_conversation_history(self, last_n: int = 5):
        """显示对话历史"""
        history = self.conversation_manager.conversation_history[-last_n:]
        if not history:
            print("📝 暂无对话历史")
            return
        
        print(f"\n📝 最近 {len(history)} 轮对话:")
        print("=" * 50)
        for i, exchange in enumerate(history, 1):
            print(f"\n第 {exchange['turn_id']} 轮 ({exchange['timestamp'].strftime('%H:%M:%S')})")
            print(f"❓ 用户: {exchange['question']}")
            print(f"🤖 助手: {exchange['answer'][:150]}...")
        print("=" * 50)

if __name__ == "__main__":
    # 测试增强版法律助手
    assistant = EnhancedLegalAssistant()
    
    print("\n🎯 多轮对话测试:")
    
    # 第一轮
    result1 = assistant.ask("试用期最长多久？")
    print(f"\n第1轮 - 问题: 试用期最长多久？")
    print(f"回答: {result1['answer'][:200]}...")
    
    # 第二轮 - 追问
    result2 = assistant.ask("那么试用期内可以随意解除合同吗？")
    print(f"\n第2轮 - 追问: 那么试用期内可以随意解除合同吗？")
    print(f"回答: {result2['answer'][:200]}...")
    print(f"是否为追问: {result2['is_follow_up']}")
    
    # 显示对话摘要
    summary = assistant.get_conversation_summary()
    print(f"\n📊 对话摘要: {summary}")
