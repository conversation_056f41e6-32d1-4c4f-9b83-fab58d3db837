"""
综合法律知识小助手 - 完整版
涵盖多个法律领域的专业咨询服务
"""
import os
import logging
from typing import List, Dict, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import openai
from dotenv import load_dotenv
from expanded_legal_kb import EXPANDED_LEGAL_DOCUMENTS, get_all_legal_types

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

class ComprehensiveLegalAssistant:
    """综合法律助手"""
    
    def __init__(self):
        # 初始化中文分词器
        def chinese_tokenizer(text):
            import re
            tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
            result = []
            for token in tokens:
                if len(token) >= 2:
                    result.append(token)
                    for i in range(len(token) - 1):
                        result.append(token[i:i+2])
            return result
        
        # 初始化向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            tokenizer=chinese_tokenizer,
            lowercase=False,
            token_pattern=None
        )
        
        # 加载法律文档
        self.documents = EXPANDED_LEGAL_DOCUMENTS
        texts = [doc['content'] for doc in self.documents]
        self.vectors = self.vectorizer.fit_transform(texts)
        
        # 初始化AI客户端
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if self.api_key:
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
        else:
            self.client = None
            logger.warning("未配置DeepSeek API密钥")
        
        print(f"✅ 法律知识库初始化完成！")
        print(f"📚 已加载 {len(self.documents)} 条法律文档")
        print(f"🏛️ 涵盖 {len(get_all_legal_types())} 个法律领域")
    
    def search_documents(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关法律文档"""
        if self.vectors is None:
            return []
        
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        top_indices = np.argsort(similarities)[::-1][:k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(similarities[idx])
                })
        
        return results
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答法律问题"""
        # 搜索相关文档
        relevant_docs = self.search_documents(question, k=5)
        
        if not relevant_docs:
            return {
                "answer": "抱歉，我没有找到相关的法律信息来回答您的问题。请尝试换个表达方式或咨询专业律师。",
                "source_documents": [],
                "legal_areas": []
            }
        
        # 分析涉及的法律领域
        legal_areas = list(set([doc['metadata']['type'] for doc in relevant_docs]))
        
        # 构建上下文
        context = "\n\n".join([f"【{doc['metadata']['type']} - {doc['metadata']['title']}】\n{doc['content']}" 
                              for doc in relevant_docs])
        
        if self.client:
            try:
                prompt = f"""你是一个专业的法律知识助手，能够回答涵盖多个法律领域的问题。请基于以下提供的法律知识库内容来回答用户的问题。

相关法律知识:
{context}

用户问题: {question}

涉及的法律领域: {', '.join(legal_areas)}

请注意:
1. 请基于提供的法律知识库内容进行回答
2. 回答要准确、专业、易懂
3. 根据问题所属的法律领域，提供相应的专业解答
4. 可以适当引用相关的法律条文或法规名称
5. 如果涉及具体的法律程序或复杂案件，请建议用户咨询专业律师
6. 如果涉及多个法律领域，请分别说明

回答:"""

                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1500,
                    temperature=0.7
                )
                
                answer = response.choices[0].message.content
                
            except Exception as e:
                logger.error(f"API调用失败: {str(e)}")
                answer = f"基于相关法律知识：{relevant_docs[0]['content'][:300]}... \n\n(注：API调用失败，显示部分相关内容，建议咨询专业律师获取完整解答)"
        else:
            # 无API时的简单回答
            answer = f"根据{relevant_docs[0]['metadata']['type']}相关规定：\n\n{relevant_docs[0]['content']}\n\n建议咨询专业律师获取更详细的法律建议。"
        
        return {
            "answer": answer,
            "source_documents": relevant_docs,
            "legal_areas": legal_areas
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {}
        for legal_type in get_all_legal_types():
            count = len([doc for doc in self.documents if doc['metadata']['type'] == legal_type])
            stats[legal_type] = count
        
        return {
            "total_documents": len(self.documents),
            "legal_areas": len(get_all_legal_types()),
            "area_distribution": stats
        }

def main():
    """主函数 - 命令行界面"""
    print("⚖️  综合法律知识小助手")
    print("=" * 60)
    print("🏛️  专业的多领域法律咨询服务")
    print("📚  基于扩展法律知识库和DeepSeek AI")
    print("=" * 60)
    
    # 初始化助手
    assistant = ComprehensiveLegalAssistant()
    
    # 显示统计信息
    stats = assistant.get_statistics()
    print(f"\n📊 知识库统计:")
    print(f"   总文档数: {stats['total_documents']} 条")
    print(f"   法律领域: {stats['legal_areas']} 个")
    print(f"\n📋 各领域文档分布:")
    for area, count in stats['area_distribution'].items():
        print(f"   {area}: {count} 条")
    
    print("\n💡 使用说明:")
    print("  • 输入您的法律问题，我会为您详细解答")
    print("  • 输入 'help' 查看示例问题")
    print("  • 输入 'stats' 查看知识库统计")
    print("  • 输入 'quit' 或 'exit' 退出程序")
    print("=" * 60)
    
    # 主循环
    while True:
        try:
            user_input = input("\n💬 请输入您的法律问题: ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("\n👋 感谢使用综合法律知识小助手，再见！")
                break
            
            if user_input.lower() in ['stats', '统计']:
                stats = assistant.get_statistics()
                print(f"\n📊 知识库详细统计:")
                for area, count in stats['area_distribution'].items():
                    print(f"   {area}: {count} 条文档")
                continue
            
            if user_input.lower() in ['help', '帮助']:
                print("\n📋 各领域示例问题:")
                examples = {
                    "农业法": ["农业补贴有哪些类型？", "土地承包权如何保护？"],
                    "劳动法": ["试用期最长多久？", "加班费如何计算？"],
                    "婚姻家庭法": ["法定结婚年龄是多少？", "离婚财产如何分割？"],
                    "房产法": ["房屋买卖需要注意什么？", "租房合同期限规定？"],
                    "消费者权益保护法": ["网购可以退货吗？", "消费欺诈如何维权？"],
                    "交通法": ["酒驾处罚标准？", "交通事故责任认定？"],
                    "刑法": ["盗窃罪量刑标准？", "故意伤害罪构成要件？"],
                    "合同法": ["合同无效的情形？", "违约责任如何承担？"]
                }
                
                for area, questions in examples.items():
                    print(f"\n🔹 {area}:")
                    for q in questions:
                        print(f"     • {q}")
                continue
            
            print("\n🔍 正在查询相关法律信息...")
            result = assistant.ask(user_input)
            
            print(f"\n🤖 法律助手回答:")
            print("-" * 50)
            print(result['answer'])
            
            if result['legal_areas']:
                print(f"\n🏛️ 涉及法律领域: {', '.join(result['legal_areas'])}")
            
            if result['source_documents']:
                print(f"\n📚 参考文档 ({len(result['source_documents'])} 个):")
                for i, doc in enumerate(result['source_documents'][:3], 1):
                    print(f"   {i}. 【{doc['metadata']['type']}】{doc['metadata']['title']}")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 处理您的问题时出现错误: {str(e)}")

if __name__ == "__main__":
    main()
