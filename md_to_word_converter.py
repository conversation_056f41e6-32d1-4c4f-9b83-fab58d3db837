"""
Markdown转Word文档转换器
将实验报告从Markdown格式转换为专业的Word文档
"""
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import re
import os

def create_word_styles(doc):
    """创建Word文档样式"""
    
    # 标题1样式
    heading1_style = doc.styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
    heading1_style.base_style = doc.styles['Heading 1']
    heading1_font = heading1_style.font
    heading1_font.name = '微软雅黑'
    heading1_font.size = Pt(18)
    heading1_font.bold = True
    heading1_font.color.rgb = RGBColor(25, 57, 138)  # 深蓝色
    
    # 标题2样式
    heading2_style = doc.styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
    heading2_style.base_style = doc.styles['Heading 2']
    heading2_font = heading2_style.font
    heading2_font.name = '微软雅黑'
    heading2_font.size = Pt(16)
    heading2_font.bold = True
    heading2_font.color.rgb = RGBColor(52, 152, 219)  # 亮蓝色
    
    # 标题3样式
    heading3_style = doc.styles.add_style('CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
    heading3_style.base_style = doc.styles['Heading 3']
    heading3_font = heading3_style.font
    heading3_font.name = '微软雅黑'
    heading3_font.size = Pt(14)
    heading3_font.bold = True
    heading3_font.color.rgb = RGBColor(46, 204, 113)  # 绿色
    
    # 正文样式
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(12)
    
    # 代码样式
    code_style = doc.styles.add_style('CodeStyle', WD_STYLE_TYPE.PARAGRAPH)
    code_font = code_style.font
    code_font.name = 'Consolas'
    code_font.size = Pt(10)
    code_style.paragraph_format.left_indent = Inches(0.5)
    
    return doc

def parse_markdown_content(content):
    """解析Markdown内容"""
    lines = content.split('\n')
    parsed_content = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            parsed_content.append({'type': 'empty', 'content': ''})
        elif line.startswith('# '):
            parsed_content.append({'type': 'h1', 'content': line[2:]})
        elif line.startswith('## '):
            parsed_content.append({'type': 'h2', 'content': line[3:]})
        elif line.startswith('### '):
            parsed_content.append({'type': 'h3', 'content': line[4:]})
        elif line.startswith('```'):
            # 代码块处理
            code_content = []
            i += 1
            while i < len(lines) and not lines[i].strip().startswith('```'):
                code_content.append(lines[i])
                i += 1
            parsed_content.append({'type': 'code', 'content': '\n'.join(code_content)})
        elif line.startswith('|') and '|' in line[1:]:
            # 表格处理
            table_content = [line]
            i += 1
            while i < len(lines) and lines[i].strip().startswith('|'):
                table_content.append(lines[i].strip())
                i += 1
            i -= 1  # 回退一行
            parsed_content.append({'type': 'table', 'content': table_content})
        elif line.startswith('- ') or line.startswith('* '):
            # 列表处理
            list_content = [line[2:]]
            i += 1
            while i < len(lines) and (lines[i].strip().startswith('- ') or lines[i].strip().startswith('* ')):
                list_content.append(lines[i].strip()[2:])
                i += 1
            i -= 1  # 回退一行
            parsed_content.append({'type': 'list', 'content': list_content})
        else:
            parsed_content.append({'type': 'paragraph', 'content': line})
        
        i += 1
    
    return parsed_content

def create_table_from_markdown(doc, table_data):
    """从Markdown表格数据创建Word表格"""
    if len(table_data) < 2:
        return
    
    # 解析表头
    headers = [cell.strip() for cell in table_data[0].split('|')[1:-1]]
    
    # 跳过分隔行，解析数据行
    rows_data = []
    for row in table_data[2:]:  # 跳过表头和分隔行
        if row.strip():
            cells = [cell.strip() for cell in row.split('|')[1:-1]]
            if cells:
                rows_data.append(cells)
    
    if not rows_data:
        return
    
    # 创建表格
    table = doc.add_table(rows=1 + len(rows_data), cols=len(headers))
    table.style = 'Table Grid'
    
    # 设置表头
    header_row = table.rows[0]
    for i, header in enumerate(headers):
        if i < len(header_row.cells):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头样式
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in paragraph.runs:
                    run.font.bold = True
                    run.font.size = Pt(11)
    
    # 填充数据行
    for row_idx, row_data in enumerate(rows_data):
        table_row = table.rows[row_idx + 1]
        for col_idx, cell_data in enumerate(row_data):
            if col_idx < len(table_row.cells):
                table_row.cells[col_idx].text = cell_data
                # 设置数据行样式
                for paragraph in table_row.cells[col_idx].paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(10)

def convert_md_to_word(md_file_path, output_file_path):
    """将Markdown文件转换为Word文档"""
    
    # 读取Markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建Word文档
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 创建样式
    doc = create_word_styles(doc)
    
    # 解析Markdown内容
    parsed_content = parse_markdown_content(content)
    
    # 转换内容
    for item in parsed_content:
        if item['type'] == 'h1':
            p = doc.add_paragraph(item['content'])
            p.style = 'CustomHeading1'
        elif item['type'] == 'h2':
            p = doc.add_paragraph(item['content'])
            p.style = 'CustomHeading2'
        elif item['type'] == 'h3':
            p = doc.add_paragraph(item['content'])
            p.style = 'CustomHeading3'
        elif item['type'] == 'paragraph':
            if item['content']:
                p = doc.add_paragraph(item['content'])
                p.style = 'Normal'
        elif item['type'] == 'code':
            p = doc.add_paragraph(item['content'])
            p.style = 'CodeStyle'
        elif item['type'] == 'table':
            create_table_from_markdown(doc, item['content'])
            doc.add_paragraph()  # 表格后添加空行
        elif item['type'] == 'list':
            for list_item in item['content']:
                p = doc.add_paragraph(list_item, style='List Bullet')
        elif item['type'] == 'empty':
            doc.add_paragraph()
    
    # 保存文档
    doc.save(output_file_path)
    return True

def main():
    """主函数"""
    print("📄 开始转换实验报告为Word格式...")
    
    # 要转换的文件列表
    files_to_convert = [
        {
            'input': '实验报告3_智能法律文书生成系统研究.md',
            'output': '实验报告3_智能法律文书生成系统研究.docx'
        },
        {
            'input': '实验报告4_法律AI系统安全性与可信度评估.md',
            'output': '实验报告4_法律AI系统安全性与可信度评估.docx'
        }
    ]
    
    success_count = 0
    
    for file_info in files_to_convert:
        input_file = file_info['input']
        output_file = file_info['output']
        
        if os.path.exists(input_file):
            try:
                print(f"🔄 正在转换: {input_file}")
                convert_md_to_word(input_file, output_file)
                
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    print(f"✅ 转换成功: {output_file}")
                    print(f"   📏 文件大小: {file_size / 1024:.1f} KB")
                    success_count += 1
                else:
                    print(f"❌ 转换失败: {output_file}")
                    
            except Exception as e:
                print(f"❌ 转换出错: {input_file}")
                print(f"   错误信息: {str(e)}")
        else:
            print(f"⚠️  文件不存在: {input_file}")
    
    print(f"\n📊 转换完成统计:")
    print(f"   ✅ 成功转换: {success_count} 个文件")
    print(f"   📁 总文件数: {len(files_to_convert)} 个文件")
    
    if success_count > 0:
        print(f"\n🎯 转换后的Word文档特点:")
        print(f"   • 专业的文档格式和样式")
        print(f"   • 清晰的标题层级结构")
        print(f"   • 美观的表格和代码块")
        print(f"   • 适合打印和分享的布局")
        print(f"\n📖 您可以使用Microsoft Word或WPS Office打开这些文档")

if __name__ == "__main__":
    main()
